/**
 * Bio-Signal Explorer v2.0 - Common JavaScript Functions
 * Author: Dr. <PERSON>
 * Institution: Sudan University of Science and Technology - BME Department
 */

// Common utility functions
const BioSignalUtils = {
    // Smooth scrolling for navigation
    initSmoothScrolling: function() {
        const navLinks = document.querySelectorAll('.nav-links a, .footer-links a');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                    
                    // Update active nav link
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
    },

    // Initialize fade-in animations
    initFadeInAnimations: function() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    },

    // Add interactive effects to cards
    initCardEffects: function() {
        const moduleCards = document.querySelectorAll('.module-card');
        moduleCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-3px) scale(1)';
            });
        });

        // Feature card stagger animation
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    },

    // Initialize pulse animation for CTA buttons
    initPulseAnimation: function() {
        const ctaButton = document.querySelector('.btn-primary');
        if (ctaButton) {
            ctaButton.classList.add('pulse');
        }
    },

    // Format numbers for display
    formatNumber: function(value, decimals = 2) {
        return parseFloat(value).toFixed(decimals);
    },

    // Generate random physiological values for simulation
    generatePhysiologicalValue: function(type) {
        switch (type) {
            case 'heartRate':
                return Math.round(60 + Math.random() * 40); // 60-100 BPM
            case 'bloodPressure':
                const systolic = Math.round(110 + Math.random() * 30); // 110-140
                const diastolic = Math.round(70 + Math.random() * 20); // 70-90
                return `${systolic}/${diastolic}`;
            case 'temperature':
                return (36.5 + Math.random()).toFixed(1); // 36.5-37.5°C
            case 'oxygenSaturation':
                return Math.round(95 + Math.random() * 5); // 95-100%
            case 'respiratoryRate':
                return Math.round(12 + Math.random() * 8); // 12-20 breaths/min
            default:
                return (Math.random() * 10).toFixed(2);
        }
    },

    // Create CSV data for export
    generateCSVData: function(signalType, duration = 10, sampleRate = 1000) {
        let csvContent = 'Time(s),Voltage(V)\n';
        
        for (let i = 0; i < duration * sampleRate; i++) {
            const time = i / sampleRate;
            let signal = 0;
            
            switch (signalType) {
                case 'ECG':
                    signal = this.generateECGSample(time);
                    break;
                case 'EMG':
                    signal = this.generateEMGSample(time);
                    break;
                case 'EEG':
                    signal = this.generateEEGSample(time);
                    break;
                default:
                    signal = Math.sin(2 * Math.PI * time) + (Math.random() - 0.5) * 0.1;
            }
            
            csvContent += `${time.toFixed(3)},${signal.toFixed(6)}\n`;
        }
        
        return csvContent;
    },

    // Generate ECG sample
    generateECGSample: function(time) {
        const heartRate = 1.2; // Hz
        const phase = (time * heartRate) % 1;
        
        if (phase < 0.1) {
            return 0.1 * Math.sin(phase * 10 * Math.PI);
        } else if (phase < 0.2) {
            return -0.3 * Math.sin((phase - 0.1) * 10 * Math.PI);
        } else if (phase < 0.3) {
            return 1.0 * Math.sin((phase - 0.2) * 10 * Math.PI);
        } else if (phase < 0.4) {
            return 0.3 * Math.sin((phase - 0.3) * 10 * Math.PI);
        } else {
            return 0.05 * Math.sin((phase - 0.4) * 5 * Math.PI);
        }
    },

    // Generate EMG sample
    generateEMGSample: function(time) {
        return (Math.random() - 0.5) * 0.5 * (1 + Math.sin(time * 2));
    },

    // Generate EEG sample
    generateEEGSample: function(time) {
        return 0.1 * (Math.sin(time * 8) + 0.5 * Math.sin(time * 12) + 0.3 * Math.sin(time * 20)) + (Math.random() - 0.5) * 0.05;
    },

    // Download file function
    downloadFile: function(content, filename, contentType = 'text/csv') {
        const blob = new Blob([content], { type: contentType });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    },

    // Show notification
    showNotification: function(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#10b981';
                break;
            case 'error':
                notification.style.backgroundColor = '#f56565';
                break;
            case 'warning':
                notification.style.backgroundColor = '#f6e05e';
                notification.style.color = '#1a202c';
                break;
            default:
                notification.style.backgroundColor = '#3b82f6';
        }
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    },

    // Initialize all common functionality
    init: function() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initSmoothScrolling();
            this.initFadeInAnimations();
            this.initCardEffects();
            this.initPulseAnimation();
        });
    }
};

// Initialize common functionality
BioSignalUtils.init();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BioSignalUtils;
}
