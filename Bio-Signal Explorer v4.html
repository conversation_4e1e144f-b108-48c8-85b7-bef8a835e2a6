<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v4.0: Complete Biomedical Device Prototyping Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-dark: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --panel-bg: #252b3d;
            --border-color: #3a4556;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --text-muted: #64748b;
            --accent-cyan: #00d4ff;
            --accent-green: #00ff88;
            --accent-yellow: #ffeb3b;
            --accent-red: #ff4757;
            --accent-purple: #8b5cf6;
            --accent-orange: #ff6b35;
            --button-bg: #2563eb;
            --button-hover: #1d4ed8;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --grid-color: #2a3441;
            --pcb-green: #1a5d1a;
            --copper-color: #cd7f32;
            --via-color: #c0c0c0;
            --trace-color: #ffd700;
            --ratsnest-color: #ff69b4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(90deg, var(--panel-bg) 0%, var(--bg-secondary) 100%);
            padding: 1rem 2rem;
            border-bottom: 2px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1800px;
            margin: 0 auto;
        }

        .header-left h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-left p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .header-right {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Mode Switcher */
        .mode-switcher {
            background: var(--bg-secondary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .mode-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .mode-btn {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--panel-bg);
            color: var(--text-primary);
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
        }

        .mode-btn:hover {
            border-color: var(--accent-cyan);
            background: var(--bg-secondary);
            transform: translateY(-2px);
        }

        .mode-btn.active {
            border-color: var(--accent-cyan);
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            color: var(--bg-dark);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .mode-icon {
            font-size: 1.2rem;
        }

        .mode-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-red);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 10px;
            font-weight: 700;
        }

        /* Main Container */
        .main-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Common Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--button-bg), var(--accent-cyan));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: var(--bg-dark);
        }

        .btn-danger {
            background: var(--error);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            min-width: 200px;
            max-height: 300px;
            overflow-y: auto;
        }

        .dropdown.active .dropdown-content {
            display: block;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: var(--bg-secondary);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .component-category {
            font-weight: 600;
            color: var(--accent-cyan);
            background: var(--bg-secondary);
        }

        .component-item {
            padding-left: 1.5rem;
            font-size: 0.85rem;
        }

        /* View Containers */
        .view-container {
            display: none;
            height: calc(100vh - 200px);
        }

        .view-container.active {
            display: flex;
        }

        /* Simulation Lab Styles */
        .simulation-lab {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }

        .control-panel, .display-area {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .display-area {
            display: flex;
            flex-direction: column;
        }

        /* Circuit Design Toolkit Styles */
        .circuit-toolkit {
            flex-direction: column;
            gap: 1rem;
        }

        .toolkit-toolbar {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .toolkit-main {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 1rem;
            flex: 1;
            min-height: 0;
        }

        .canvas-container {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        .circuit-canvas {
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(var(--grid-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
            background-size: 20px 20px;
            border-radius: 8px;
            cursor: crosshair;
        }

        .analysis-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* PCB Workbench Styles */
        .pcb-workbench {
            flex-direction: row;
            gap: 1rem;
        }

        .pcb-left-panel {
            width: 280px;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-center {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-right-panel {
            width: 320px;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .pcb-canvas-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .pcb-canvas {
            width: 100%;
            height: 100%;
            background: var(--pcb-green);
            border-radius: 8px;
            cursor: crosshair;
        }

        .component-bin {
            max-height: 300px;
            overflow-y: auto;
        }

        .component-bin-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: grab;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .component-bin-item:hover {
            background: var(--border-color);
            transform: translateY(-2px);
        }

        .component-bin-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .layer-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-secondary);
            border-radius: 6px;
        }

        .layer-checkbox {
            width: 1rem;
            height: 1rem;
        }

        .drc-status {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .drc-pass {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid var(--success);
            color: var(--success);
        }

        .drc-fail {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid var(--error);
            color: var(--error);
        }

        .preview-3d {
            height: 200px;
            background: #1a1a1a;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .preview-3d canvas {
            width: 100%;
            height: 100%;
        }

        /* Animation Classes */
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px var(--accent-cyan); }
            50% { box-shadow: 0 0 20px var(--accent-cyan), 0 0 30px var(--accent-cyan); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes waveform {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .animated-icon {
            animation: pulse 2s infinite;
        }

        .heartbeat-icon {
            animation: heartbeat 1.2s infinite;
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .glow-effect {
            animation: glow 2s infinite;
        }

        .rotating {
            animation: rotate 2s linear infinite;
        }

        /* Interactive Elements */
        .interactive-demo {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin: 1rem 0;
            position: relative;
            overflow: hidden;
        }

        .demo-waveform {
            height: 100px;
            background: #000;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .waveform-line {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--accent-green);
            transform: translateY(-50%);
        }

        .waveform-trace {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, var(--accent-green) 50%, transparent 100%);
            animation: waveform 2s linear infinite;
        }

        /* Circuit Diagram Styles */
        .circuit-diagram {
            background: #f8f9fa;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin: 1rem 0;
            position: relative;
        }

        .circuit-component {
            position: absolute;
            background: white;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 0.5rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .circuit-component:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .circuit-wire {
            position: absolute;
            background: #333;
            z-index: 1;
        }

        .signal-flow {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--accent-cyan);
            border-radius: 50%;
            animation: pulse 1s infinite;
        }

        /* Block Diagram Styles */
        .block-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            margin: 1rem 0;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .block {
            background: var(--panel-bg);
            border: 2px solid var(--accent-cyan);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            min-width: 120px;
            position: relative;
            transition: all 0.3s ease;
        }

        .block:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .block-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .block-arrow {
            font-size: 1.5rem;
            color: var(--accent-yellow);
            animation: pulse 2s infinite;
        }

        /* Real Photo Integration */
        .demo-photo {
            width: 100%;
            max-width: 300px;
            height: 200px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            margin: 1rem auto;
            position: relative;
            overflow: hidden;
        }

        .demo-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .demo-photo-content {
            position: relative;
            z-index: 1;
        }

        /* Interactive Controls */
        .interactive-control {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
        }

        .control-slider {
            width: 100%;
            margin: 0.5rem 0;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: var(--bg-secondary);
            outline: none;
        }

        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-cyan);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 212, 255, 0.3);
        }

        .control-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent-cyan);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 212, 255, 0.3);
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-online {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .status-offline {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error);
            border: 1px solid var(--error);
        }

        .status-processing {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning);
            border: 1px solid var(--warning);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        /* Enhanced Oscilloscope Styles */
        .oscilloscope-container {
            flex: 1;
            background: var(--panel-bg);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .oscilloscope-header {
            background: var(--bg-secondary);
            padding: 0.75rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .oscilloscope-title {
            font-weight: 600;
            color: var(--accent-cyan);
        }

        .oscilloscope-controls {
            display: flex;
            gap: 0.5rem;
        }

        .btn-mini {
            padding: 0.25rem 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--panel-bg);
            color: var(--text-primary);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-mini:hover {
            background: var(--accent-cyan);
            color: var(--bg-dark);
        }

        .oscilloscope-display {
            position: relative;
            height: 300px;
            background: #000;
        }

        .oscilloscope-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .measurement-cursors {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .cursor-v1, .cursor-v2 {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--accent-yellow);
            opacity: 0.8;
            display: none;
        }

        .measurement-readout {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: var(--accent-green);
            padding: 0.5rem;
            border-radius: 4px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.75rem;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        /* Virtual LCD Enhancement */
        .virtual-lcd {
            background: #000;
            color: var(--accent-green);
            font-family: 'Roboto Mono', monospace;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            min-height: 80px;
            border: 2px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .virtual-lcd::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 225, 255, 0.1) 50%, transparent 100%);
            animation: waveform 3s linear infinite;
        }

        .lcd-text {
            position: relative;
            z-index: 1;
        }

        /* Tab Enhancement */
        .tab-container {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }

        .tab-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-btn:hover {
            color: var(--accent-cyan);
            background: var(--hover-bg);
        }

        .tab-btn.active {
            color: var(--accent-cyan);
            border-bottom-color: var(--accent-cyan);
        }

        .tab-content {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 8px;
            min-height: 120px;
            animation: fadeInUp 0.3s ease;
        }

        /* Language Toggle Styles */
        .language-toggle {
            margin-right: 1rem;
        }

        .language-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            background: var(--panel-bg);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .language-btn:hover {
            border-color: var(--accent-cyan);
            background: var(--bg-secondary);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .language-btn.switching {
            animation: languageSwitch 0.6s ease;
        }

        @keyframes languageSwitch {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(180deg); background: var(--accent-cyan); }
            100% { transform: scale(1) rotate(360deg); }
        }

        .lang-icon {
            font-size: 1.2rem;
            animation: rotate 3s linear infinite;
        }

        .lang-text {
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }

        .lang-arrow {
            font-size: 1rem;
            opacity: 0.7;
            animation: pulse 2s infinite;
        }

        /* RTL Support */
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .rtl .header-content {
            flex-direction: row-reverse;
        }

        .rtl .header-right {
            flex-direction: row-reverse;
        }

        .rtl .mode-buttons {
            flex-direction: row-reverse;
        }

        .rtl .simulation-lab {
            grid-template-columns: 2fr 1fr;
        }

        .rtl .toolkit-main {
            grid-template-columns: 300px 1fr;
        }

        .rtl .pcb-workbench {
            flex-direction: row-reverse;
        }

        .rtl .block-diagram {
            flex-direction: row-reverse;
        }

        .rtl .block-arrow {
            transform: scaleX(-1);
        }

        /* Arabic Font Support */
        .arabic-text {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            font-size: 1.1em;
            line-height: 1.6;
        }

        /* Language-specific visibility */
        [data-lang="en"] .ar-only {
            display: none !important;
        }

        [data-lang="ar"] .en-only {
            display: none !important;
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .simulation-lab {
                grid-template-columns: 1fr;
            }
            
            .toolkit-main {
                grid-template-columns: 1fr;
            }
            
            .pcb-workbench {
                flex-direction: column;
            }
            
            .pcb-left-panel,
            .pcb-right-panel {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .mode-buttons {
                flex-direction: column;
            }
            
            .main-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <h1>Bio-Signal Explorer v4.0</h1>
                <p>Complete Biomedical Device Prototyping Platform</p>
            </div>
            <div class="header-right">
                <!-- Language Toggle Switch -->
                <div class="language-toggle">
                    <button type="button" class="language-btn" id="langToggle" onclick="toggleLanguage()">
                        <span class="lang-icon">🌐</span>
                        <span class="lang-text" data-en="English" data-ar="العربية">English</span>
                        <span class="lang-arrow">⇄</span>
                    </button>
                </div>

                <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <span data-en="🏠 Home" data-ar="🏠 الرئيسية">🏠 Home</span>
                </button>
                <button type="button" class="btn btn-secondary" onclick="showHelp()">
                    <span data-en="❓ Help" data-ar="❓ مساعدة">❓ Help</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Mode Switcher -->
    <div class="mode-switcher">
        <div class="mode-buttons">
            <button type="button" class="mode-btn active" id="simModeBtn" onclick="switchMode('simulation')">
                <span class="mode-icon">🔬</span>
                <span data-en="Simulation Lab" data-ar="مختبر المحاكاة">Simulation Lab</span>
                <span class="mode-badge">1</span>
            </button>
            <button type="button" class="mode-btn" id="designModeBtn" onclick="switchMode('design')">
                <span class="mode-icon">⚡</span>
                <span data-en="Circuit Design Toolkit" data-ar="أدوات تصميم الدوائر">Circuit Design Toolkit</span>
                <span class="mode-badge">2</span>
            </button>
            <button type="button" class="mode-btn" id="pcbModeBtn" onclick="switchMode('pcb')">
                <span class="mode-icon">🔧</span>
                <span data-en="PCB Workbench" data-ar="ورشة تصميم اللوحات">PCB Workbench</span>
                <span class="mode-badge">3</span>
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Simulation Lab View -->
        <div class="view-container active" id="simulationView">
            <div class="simulation-lab">
                <!-- Control Panel -->
                <div class="control-panel">
                    <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">
                        <span data-en="🎛️ Control Hub" data-ar="🎛️ مركز التحكم">🎛️ Control Hub</span>
                    </h2>

                    <!-- Power Control -->
                    <div style="margin-bottom: 1.5rem;">
                        <button type="button" class="btn btn-success" id="powerBtn" onclick="togglePower()" style="width: 100%;">
                            <span data-en="⚡ Power On" data-ar="⚡ تشغيل النظام">⚡ Power On</span>
                        </button>
                    </div>

                    <!-- Module Selection -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
                            <span data-en="Measurement Module:" data-ar="وحدة القياس:">Measurement Module:</span>
                        </label>
                        <div style="display: flex; gap: 0.5rem;">
                            <select id="moduleSelect" style="flex: 1; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); color: var(--text-primary);" onchange="selectModule()">
                                <option value="" data-en="Select Module..." data-ar="اختر الوحدة...">Select Module...</option>
                                <option value="ecg" data-en="ECG - Electrocardiogram" data-ar="تخطيط القلب الكهربائي">ECG - Electrocardiogram</option>
                                <option value="emg" data-en="EMG - Electromyography" data-ar="تخطيط العضلات الكهربائي">EMG - Electromyography</option>
                                <option value="eeg" data-en="EEG - Electroencephalography" data-ar="تخطيط الدماغ الكهربائي">EEG - Electroencephalography</option>
                                <option value="eog" data-en="EOG - Electrooculography" data-ar="تخطيط العين الكهربائي">EOG - Electrooculography</option>
                                <option value="bp" data-en="Blood Pressure" data-ar="ضغط الدم">Blood Pressure</option>
                                <option value="impedance" data-en="Body Impedance" data-ar="مقاومة الجسم">Body Impedance</option>
                            </select>
                            <button type="button" class="btn btn-secondary" id="viewCircuitBtn" onclick="viewInternalCircuit()" disabled>
                                <span data-en="🔍" data-ar="🔍" title-en="View Internal Circuit" title-ar="عرض الدائرة الداخلية">🔍</span>
                            </button>
                        </div>
                    </div>

                    <!-- Sensor Connections -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
                            <span data-en="Sensor Connections:" data-ar="توصيلات أجهزة الاستشعار:">Sensor Connections:</span>
                        </label>
                        <div id="sensorGrid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem;">
                            <!-- Sensors will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Function Generator -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
                            <span data-en="Function Generator:" data-ar="مولد الإشارات:">Function Generator:</span>
                        </label>
                        <div style="margin-bottom: 0.75rem;">
                            <label style="font-size: 0.85rem; color: var(--text-secondary);">
                                <span data-en="Frequency:" data-ar="التردد:">Frequency:</span> <span id="freqValue">100</span> Hz
                            </label>
                            <input type="range" id="freqSlider" min="1" max="1000" value="100" style="width: 100%; margin-top: 0.25rem;" oninput="updateFrequency()">
                        </div>
                        <div>
                            <label style="font-size: 0.85rem; color: var(--text-secondary);">
                                <span data-en="Amplitude:" data-ar="السعة:">Amplitude:</span> <span id="ampValue">1.0</span> Vpp
                            </label>
                            <input type="range" id="ampSlider" min="0.1" max="10" step="0.1" value="1.0" style="width: 100%; margin-top: 0.25rem;" oninput="updateAmplitude()">
                        </div>
                    </div>

                    <!-- Analysis Mode -->
                    <div>
                        <button type="button" class="btn btn-warning" id="analysisBtn" onclick="toggleAnalysisMode()" style="width: 100%;" disabled>
                            <span data-en="📊 Enter Circuit Analysis" data-ar="📊 دخول وضع التحليل">📊 Enter Circuit Analysis</span>
                        </button>
                    </div>
                </div>

                <!-- Display Area -->
                <div class="display-area">
                    <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">📺 System Display</h2>

                    <!-- Status Panel with Animation -->
                    <div id="statusPanel" class="status-indicator status-offline">
                        <span class="status-dot"></span>
                        <span>System is powered off. Click Power On to begin.</span>
                    </div>

                    <!-- Interactive Demo Section -->
                    <div class="interactive-demo fade-in-up">
                        <h4 style="color: var(--accent-cyan); margin-bottom: 1rem;">📊 Live Signal Preview</h4>
                        <div class="demo-waveform">
                            <div class="waveform-line"></div>
                            <div class="waveform-trace" id="previewTrace"></div>
                            <canvas id="miniOscilloscope" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"></canvas>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.5rem; font-size: 0.85rem; color: var(--text-secondary);">
                            <span>📈 Amplitude: <span id="previewAmp">0.0V</span></span>
                            <span>🔄 Frequency: <span id="previewFreq">0Hz</span></span>
                            <span>💓 Heart Rate: <span id="previewHR">--</span></span>
                        </div>
                    </div>

                    <!-- Module Information Cards -->
                    <div id="moduleInfoCards" class="slide-in">
                        <!-- Cards will be populated by JavaScript -->
                    </div>

                    <!-- Enhanced Virtual LCD -->
                    <div id="virtualLCD" class="virtual-lcd">
                        <div class="lcd-text">
                            > SYSTEM OFFLINE<br>
                            > POWER ON TO START
                        </div>
                    </div>

                    <!-- Enhanced Oscilloscope -->
                    <div class="oscilloscope-container">
                        <div class="oscilloscope-header">
                            <span class="oscilloscope-title">🔬 Digital Oscilloscope</span>
                            <div class="oscilloscope-controls">
                                <button type="button" class="btn-mini" onclick="toggleOscilloscopeGrid()">Grid</button>
                                <button type="button" class="btn-mini" onclick="captureWaveform()">Capture</button>
                                <button type="button" class="btn-mini" onclick="resetOscilloscope()">Reset</button>
                            </div>
                        </div>
                        <div class="oscilloscope-display">
                            <canvas id="oscilloscope"></canvas>
                            <div class="oscilloscope-overlay">
                                <div class="measurement-cursors">
                                    <div class="cursor-v1" id="cursorV1"></div>
                                    <div class="cursor-v2" id="cursorV2"></div>
                                </div>
                                <div class="measurement-readout">
                                    <span>ΔV: <span id="deltaV">0.0V</span></span>
                                    <span>ΔT: <span id="deltaT">0.0ms</span></span>
                                    <span>Freq: <span id="measuredFreq">0Hz</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Tabs -->
                    <div class="tab-container">
                        <button type="button" class="tab-btn active" onclick="showTab('info')">
                            📋 Module Info
                        </button>
                        <button type="button" class="tab-btn" onclick="showTab('procedure')">
                            📝 Procedure
                        </button>
                        <button type="button" class="tab-btn" onclick="showTab('export')">
                            💾 Export Data
                        </button>
                    </div>

                    <!-- Enhanced Tab Content -->
                    <div id="tabContent" class="tab-content">
                        <div class="welcome-content">
                            <div class="demo-photo">
                                <div class="demo-photo-content">
                                    <div style="font-size: 3rem; margin-bottom: 1rem;">🔬</div>
                                    <div>Select a measurement module to begin</div>
                                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 0.5rem;">Professional Biomedical Instrumentation</div>
                                </div>
                            </div>

                            <!-- Quick Start Block Diagram -->
                            <div class="block-diagram">
                                <div class="block">
                                    <span class="block-icon animated-icon">⚡</span>
                                    <div>Power On</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon">🔧</span>
                                    <div>Select Module</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon">🔌</span>
                                    <div>Connect Sensors</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon heartbeat-icon">📊</span>
                                    <div>Measure Signal</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Circuit Design Toolkit View -->
        <div class="view-container" id="designView">
            <div class="circuit-toolkit">
                <!-- Toolbar -->
                <div class="toolkit-toolbar">
                    <!-- Component Library -->
                    <div class="dropdown" id="componentDropdown">
                        <button type="button" class="dropdown-btn" onclick="toggleDropdown('componentDropdown')">
                            🧩 Add Component
                            <span style="margin-left: auto;">▼</span>
                        </button>
                        <div class="dropdown-content">
                            <div class="dropdown-item component-category">Sources</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('function-generator')">📡 Function Generator</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('bio-signal')">💓 Bio-Signal Source</div>

                            <div class="dropdown-item component-category">Passive Components</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('resistor')">🔧 Resistor</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('capacitor')">⚡ Capacitor</div>

                            <div class="dropdown-item component-category">Active Components</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('op-amp')">🔺 Op-Amp</div>

                            <div class="dropdown-item component-category">Measurement</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('oscilloscope')">📊 Oscilloscope Probe</div>
                        </div>
                    </div>

                    <!-- Template Library -->
                    <div class="dropdown" id="templateDropdown">
                        <button type="button" class="dropdown-btn" onclick="toggleDropdown('templateDropdown')">
                            📋 Load Template
                            <span style="margin-left: auto;">▼</span>
                        </button>
                        <div class="dropdown-content">
                            <div class="dropdown-item" onclick="loadTemplate('rc-lowpass')">RC Low-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('rc-highpass')">RC High-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('non-inverting-amp')">Non-inverting Op-Amp</div>
                            <div class="dropdown-item" onclick="loadTemplate('bandpass-filter')">Band-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('ecg-frontend')">ECG Frontend Circuit</div>
                            <div class="dropdown-item" onclick="loadTemplate('emg-amplifier')">EMG Amplifier</div>
                        </div>
                    </div>

                    <!-- Simulation Controls -->
                    <div style="display: flex; gap: 0.5rem;">
                        <button type="button" class="btn btn-success" id="runSimBtn" onclick="runCircuitSimulation()">
                            ▶️ Run Simulation
                        </button>
                        <button type="button" class="btn btn-danger" id="stopSimBtn" onclick="stopCircuitSimulation()" disabled>
                            ⏹️ Stop
                        </button>
                    </div>

                    <!-- Analysis Tools -->
                    <div style="display: flex; gap: 0.5rem;">
                        <button type="button" class="btn btn-primary" onclick="generateBodePlot()">
                            📈 Bode Plot
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearCanvas()">
                            🗑️ Clear
                        </button>
                    </div>

                    <!-- PCB Transition -->
                    <div style="margin-left: auto;">
                        <button type="button" class="btn btn-warning" id="proceedToPCBBtn" onclick="proceedToPCB()" disabled>
                            🔧 Proceed to PCB Layout
                        </button>
                    </div>
                </div>

                <!-- Main Toolkit Area -->
                <div class="toolkit-main">
                    <!-- Canvas Container -->
                    <div class="canvas-container">
                        <canvas id="circuitCanvas" class="circuit-canvas"></canvas>

                        <!-- Component Properties Panel -->
                        <div id="propertiesPanel" class="hidden" style="position: absolute; top: 20px; right: 20px; background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 8px; padding: 1rem; min-width: 200px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
                            <h4 style="margin-bottom: 0.5rem; color: var(--accent-cyan);">Component Properties</h4>
                            <div id="propertiesContent">
                                <!-- Properties will be populated by JavaScript -->
                            </div>
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                                <button type="button" class="btn btn-primary" onclick="applyProperties()" style="flex: 1; font-size: 0.8rem;">Apply</button>
                                <button type="button" class="btn btn-secondary" onclick="closeProperties()" style="flex: 1; font-size: 0.8rem;">Close</button>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Panel -->
                    <div class="analysis-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📊 Analysis</h3>

                        <!-- Analysis Tabs -->
                        <div style="display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem;">
                            <button type="button" class="analysis-tab active" onclick="showAnalysisTab('oscilloscope')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                                📺 Scope
                            </button>
                            <button type="button" class="analysis-tab" onclick="showAnalysisTab('bode')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                                📈 Bode
                            </button>
                        </div>

                        <!-- Analysis Content -->
                        <div id="analysisContent" style="flex: 1; background: #000; border: 1px solid var(--border-color); border-radius: 8px; position: relative; min-height: 200px;">
                            <canvas id="analysisCanvas" style="width: 100%; height: 100%; border-radius: 7px;"></canvas>
                            <div id="analysisPlaceholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-muted); text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                                <div>Run simulation to see results</div>
                            </div>
                        </div>

                        <!-- Analysis Info -->
                        <div id="analysisInfo" style="margin-top: 1rem; padding: 0.75rem; background: var(--bg-secondary); border-radius: 8px; font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Status:</strong> <span id="simStatus">Ready</span></div>
                            <div><strong>Components:</strong> <span id="componentCount">0</span></div>
                            <div><strong>Connections:</strong> <span id="connectionCount">0</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PCB Workbench View -->
        <div class="view-container" id="pcbView">
            <div class="pcb-workbench">
                <!-- Left Panel -->
                <div class="pcb-left-panel">
                    <!-- Component Bin -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📦 Component Bin</h3>
                        <div class="component-bin" id="componentBin">
                            <!-- Components will be populated from circuit design -->
                        </div>
                    </div>

                    <!-- Layers Panel -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📋 Layers</h3>
                        <div id="layersPanel">
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="topCopperLayer" checked onchange="toggleLayer('topCopper')">
                                <label for="topCopperLayer">Top Copper</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="bottomCopperLayer" checked onchange="toggleLayer('bottomCopper')">
                                <label for="bottomCopperLayer">Bottom Copper</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="silkscreenLayer" checked onchange="toggleLayer('silkscreen')">
                                <label for="silkscreenLayer">Silkscreen</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="drillLayer" checked onchange="toggleLayer('drill')">
                                <label for="drillLayer">Drill Holes</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="ratsnestLayer" checked onchange="toggleLayer('ratsnest')">
                                <label for="ratsnestLayer">Ratsnest</label>
                            </div>
                        </div>
                    </div>

                    <!-- PCB Tools -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">🛠️ Tools</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <button type="button" class="btn btn-secondary" id="selectTool" onclick="selectPCBTool('select')" style="width: 100%;">
                                🖱️ Select
                            </button>
                            <button type="button" class="btn btn-secondary" id="routeTool" onclick="selectPCBTool('route')" style="width: 100%;">
                                🔗 Route Trace
                            </button>
                            <button type="button" class="btn btn-secondary" id="viaTool" onclick="selectPCBTool('via')" style="width: 100%;">
                                ⚫ Place Via
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearPCB()" style="width: 100%;">
                                🗑️ Clear PCB
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Center Panel -->
                <div class="pcb-center">
                    <!-- PCB Toolbar -->
                    <div class="pcb-panel" style="padding: 0.75rem;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <label style="font-size: 0.9rem;">Layer:</label>
                                <select id="currentLayer" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary);">
                                    <option value="top">Top Copper</option>
                                    <option value="bottom">Bottom Copper</option>
                                </select>
                            </div>
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <label style="font-size: 0.9rem;">Grid:</label>
                                <select id="gridSize" onchange="updateGrid()" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary);">
                                    <option value="0.1">0.1 inch</option>
                                    <option value="0.05">0.05 inch</option>
                                    <option value="0.025">0.025 inch</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="autoRoute()" style="margin-left: auto;">
                                🤖 Auto Route
                            </button>
                        </div>
                    </div>

                    <!-- PCB Canvas -->
                    <div class="pcb-panel pcb-canvas-container">
                        <canvas id="pcbCanvas" class="pcb-canvas"></canvas>
                    </div>
                </div>

                <!-- Right Panel -->
                <div class="pcb-right-panel">
                    <!-- Design Rule Checker -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">✅ Design Rules</h3>
                        <div id="drcStatus" class="drc-status drc-pass">
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">✅ DRC: PASS</div>
                            <div style="font-size: 0.85rem;">All design rules satisfied</div>
                        </div>
                        <div style="font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Nets Remaining:</strong> <span id="netsRemaining">0</span></div>
                            <div><strong>Components:</strong> <span id="pcbComponentCount">0</span></div>
                            <div><strong>Traces:</strong> <span id="traceCount">0</span></div>
                            <div><strong>Vias:</strong> <span id="viaCount">0</span></div>
                        </div>
                    </div>

                    <!-- 3D Preview -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">🎯 3D Preview</h3>
                        <div class="preview-3d" id="preview3D">
                            <canvas id="preview3DCanvas" style="width: 100%; height: 100%;"></canvas>
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-muted); text-align: center; font-size: 0.85rem;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔧</div>
                                <div>Place components to see 3D view</div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Panel -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📤 Export</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <button type="button" class="btn btn-success" onclick="exportGerber()" style="width: 100%;">
                                📄 Export Gerber & Drill Files
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="exportBOM()" style="width: 100%;">
                                📋 Export Bill of Materials
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="export3D()" style="width: 100%;">
                                🎯 Export 3D Model
                            </button>
                        </div>
                    </div>

                    <!-- PCB Statistics -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📊 Statistics</h3>
                        <div style="font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Board Size:</strong> <span id="boardSize">50mm x 50mm</span></div>
                            <div><strong>Total Trace Length:</strong> <span id="totalTraceLength">0 mm</span></div>
                            <div><strong>Layer Count:</strong> <span id="layerCount">2</span></div>
                            <div><strong>Completion:</strong> <span id="completionPercent">0%</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
        <div style="background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 16px; padding: 2rem; max-width: 700px; max-height: 80vh; overflow-y: auto; margin: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h2 style="color: var(--accent-cyan);">
                    <span data-en="🎓 Bio-Signal Explorer v4.0 Guide" data-ar="🎓 دليل مستكشف الإشارات الحيوية الإصدار 4.0">🎓 Bio-Signal Explorer v4.0 Guide</span>
                </h2>
                <button type="button" onclick="hideHelp()" style="background: none; border: none; color: var(--text-secondary); font-size: 1.5rem; cursor: pointer;">×</button>
            </div>

            <div style="space-y: 1rem;">
                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔬 1. Simulation Lab</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Start here to understand biomedical measurement concepts. Power on the system, select a module, connect sensors, and observe real-time physiological signals.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">⚡ 2. Circuit Design Toolkit</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Design and analyze circuits from scratch. Add components, connect them, run simulations, and generate Bode plots. Use the "View Internal Circuit" button to explore module designs.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔧 3. PCB Workbench</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">NEW! Transform your circuit into a physical PCB layout. Place components, route traces, check design rules, and view real-time 3D preview. Export Gerber files for manufacturing.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔄 Workflow Integration</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Follow the complete prototyping lifecycle: Simulation → Circuit Design → PCB Layout. Use "Proceed to PCB Layout" to automatically transfer your circuit design.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">⌨️ Keyboard Shortcuts</h3>
                    <div style="color: var(--text-secondary); line-height: 1.6;">
                        <div>• <strong>Ctrl+1:</strong> Switch to Simulation Lab</div>
                        <div>• <strong>Ctrl+2:</strong> Switch to Circuit Design</div>
                        <div>• <strong>Ctrl+3:</strong> Switch to PCB Workbench</div>
                        <div>• <strong>Ctrl+S:</strong> Start/Stop simulation</div>
                        <div>• <strong>Escape:</strong> Close modals and dropdowns</div>
                    </div>
                </div>

                <div>
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🎯 PCB Tools</h3>
                    <div style="color: var(--text-secondary); line-height: 1.6;">
                        <div>• <strong>Select Tool:</strong> Move and rotate components</div>
                        <div>• <strong>Route Tool:</strong> Draw copper traces between pads</div>
                        <div>• <strong>Via Tool:</strong> Connect between layers</div>
                        <div>• <strong>Auto Route:</strong> Automatic trace routing</div>
                        <div>• <strong>DRC:</strong> Real-time design rule checking</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Success Modal -->
    <div id="exportModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
        <div style="background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 16px; padding: 2rem; max-width: 500px; margin: 2rem; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
            <h2 style="color: var(--accent-green); margin-bottom: 1rem;">Export Successful!</h2>
            <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">The following manufacturing files have been generated:</p>
            <div id="exportFileList" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; text-align: left; font-family: 'Roboto Mono', monospace; font-size: 0.85rem;">
                <!-- File list will be populated by JavaScript -->
            </div>
            <button type="button" class="btn btn-primary" onclick="closeExportModal()">Close</button>
        </div>
    </div>

    <script>
        // Global State Management
        const AppState = {
            currentMode: 'simulation',
            powerOn: false,
            selectedModule: null,
            analysisMode: false,
            currentLanguage: 'en', // Default to English
            circuitSimulation: {
                running: false,
                components: [],
                connections: [],
                selectedComponent: null,
                draggedComponent: null,
                readyForPCB: false
            },
            pcbWorkbench: {
                components: [],
                traces: [],
                vias: [],
                ratsnest: [],
                selectedTool: 'select',
                currentLayer: 'top',
                gridSize: 0.1,
                placedComponents: [],
                drcStatus: 'pass'
            },
            animationFrameId: null
        };

        // Comprehensive Language Translations
        const Translations = {
            en: {
                // Header
                title: "Bio-Signal Explorer v4.0",
                subtitle: "Complete Biomedical Device Prototyping Platform",
                home: "🏠 Home",
                help: "❓ Help",
                language: "English",

                // Mode Switcher
                simulationLab: "Simulation Lab",
                circuitDesign: "Circuit Design Toolkit",
                pcbWorkbench: "PCB Workbench",

                // Control Hub
                controlHub: "🎛️ Control Hub",
                powerOn: "⚡ Power On",
                powerOff: "🔴 Power Off",
                measurementModule: "Measurement Module:",
                selectModule: "Select Module...",
                sensorConnections: "Sensor Connections:",
                functionGenerator: "Function Generator:",
                frequency: "Frequency:",
                amplitude: "Amplitude:",
                enterAnalysis: "📊 Enter Circuit Analysis",
                exitAnalysis: "📊 Exit Analysis Mode",

                // Modules
                ecg: "ECG - Electrocardiogram",
                emg: "EMG - Electromyography",
                eeg: "EEG - Electroencephalography",
                eog: "EOG - Electrooculography",
                bloodPressure: "Blood Pressure",
                bodyImpedance: "Body Impedance",

                // Status Messages
                systemOffline: "System is powered off. Click Power On to begin.",
                systemOnline: "System powered on. Select a measurement module.",
                selectModuleToBegin: "Select a measurement module to begin",
                connectSensors: "CONNECT SENSORS TO BEGIN",

                // Tabs
                moduleInfo: "📋 Module Info",
                procedure: "📝 Procedure",
                exportData: "💾 Export Data",

                // Oscilloscope
                digitalOscilloscope: "🔬 Digital Oscilloscope",
                grid: "Grid",
                capture: "Capture",
                reset: "Reset",

                // Circuit Design
                addComponent: "🧩 Add Component",
                loadTemplate: "📋 Load Template",
                runSimulation: "▶️ Run Simulation",
                stop: "⏹️ Stop",
                bodePlot: "📈 Bode Plot",
                clear: "🗑️ Clear",
                proceedToPCB: "🔧 Proceed to PCB Layout",

                // PCB Workbench
                componentBin: "📦 Component Bin",
                layers: "📋 Layers",
                tools: "🛠️ Tools",
                select: "🖱️ Select",
                routeTrace: "🔗 Route Trace",
                placeVia: "⚫ Place Via",
                clearPCB: "🗑️ Clear PCB",
                autoRoute: "🤖 Auto Route",
                designRules: "✅ Design Rules",
                preview3D: "🎯 3D Preview",
                export: "📤 Export",
                statistics: "📊 Statistics"
            },
            ar: {
                // Header
                title: "مستكشف الإشارات الحيوية الإصدار 4.0",
                subtitle: "منصة شاملة لتطوير الأجهزة الطبية الحيوية",
                home: "🏠 الرئيسية",
                help: "❓ مساعدة",
                language: "العربية",

                // Mode Switcher
                simulationLab: "مختبر المحاكاة",
                circuitDesign: "أدوات تصميم الدوائر",
                pcbWorkbench: "ورشة تصميم اللوحات",

                // Control Hub
                controlHub: "🎛️ مركز التحكم",
                powerOn: "⚡ تشغيل النظام",
                powerOff: "🔴 إيقاف النظام",
                measurementModule: "وحدة القياس:",
                selectModule: "اختر الوحدة...",
                sensorConnections: "توصيلات أجهزة الاستشعار:",
                functionGenerator: "مولد الإشارات:",
                frequency: "التردد:",
                amplitude: "السعة:",
                enterAnalysis: "📊 دخول وضع التحليل",
                exitAnalysis: "📊 خروج من وضع التحليل",

                // Modules
                ecg: "تخطيط القلب الكهربائي",
                emg: "تخطيط العضلات الكهربائي",
                eeg: "تخطيط الدماغ الكهربائي",
                eog: "تخطيط العين الكهربائي",
                bloodPressure: "ضغط الدم",
                bodyImpedance: "مقاومة الجسم",

                // Status Messages
                systemOffline: "النظام مغلق. اضغط تشغيل النظام للبدء.",
                systemOnline: "النظام يعمل. اختر وحدة قياس.",
                selectModuleToBegin: "اختر وحدة قياس للبدء",
                connectSensors: "قم بتوصيل أجهزة الاستشعار للبدء",

                // Tabs
                moduleInfo: "📋 معلومات الوحدة",
                procedure: "📝 الإجراءات",
                exportData: "💾 تصدير البيانات",

                // Oscilloscope
                digitalOscilloscope: "🔬 راسم الذبذبات الرقمي",
                grid: "الشبكة",
                capture: "التقاط",
                reset: "إعادة تعيين",

                // Circuit Design
                addComponent: "🧩 إضافة مكون",
                loadTemplate: "📋 تحميل قالب",
                runSimulation: "▶️ تشغيل المحاكاة",
                stop: "⏹️ إيقاف",
                bodePlot: "📈 مخطط بود",
                clear: "🗑️ مسح",
                proceedToPCB: "🔧 الانتقال لتصميم اللوحة",

                // PCB Workbench
                componentBin: "📦 صندوق المكونات",
                layers: "📋 الطبقات",
                tools: "🛠️ الأدوات",
                select: "🖱️ تحديد",
                routeTrace: "🔗 توصيل المسارات",
                placeVia: "⚫ وضع فتحة",
                clearPCB: "🗑️ مسح اللوحة",
                autoRoute: "🤖 توصيل تلقائي",
                designRules: "✅ قواعد التصميم",
                preview3D: "🎯 معاينة ثلاثية الأبعاد",
                export: "📤 تصدير",
                statistics: "📊 الإحصائيات"
            }
        };

        // Module Definitions with Bilingual Support
        const LabModules = {
            ecg: {
                name: {
                    en: "ECG - Electrocardiogram",
                    ar: "تخطيط القلب الكهربائي"
                },
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN5'],
                description: {
                    en: "Measures electrical activity of the heart through electrodes placed on the skin.",
                    ar: "يقيس النشاط الكهربائي للقلب من خلال أقطاب كهربائية موضوعة على الجلد."
                },
                procedure: {
                    en: "1. Clean skin areas\n2. Attach electrodes to RA, LA, LL, RL\n3. Ask subject to relax\n4. Record P-QRS-T waveform",
                    ar: "1. تنظيف مناطق الجلد\n2. تثبيت الأقطاب على RA, LA, LL, RL\n3. اطلب من المريض الاسترخاء\n4. تسجيل موجة P-QRS-T"
                },
                circuitTemplate: 'ecg-frontend',
                frequency: 1.2,
                amplitude: 1.0
            },
            emg: {
                name: {
                    en: "EMG - Electromyography",
                    ar: "تخطيط العضلات الكهربائي"
                },
                requiredSensors: ['IN1', 'IN2', 'IN5'],
                description: {
                    en: "Records electrical potential generated by muscle cells during activation.",
                    ar: "يسجل الجهد الكهربائي المتولد من خلايا العضلات أثناء التنشيط."
                },
                procedure: {
                    en: "1. Place electrodes over target muscle\n2. Place ground electrode on bony prominence\n3. Record during muscle contractions",
                    ar: "1. ضع الأقطاب فوق العضلة المستهدفة\n2. ضع القطب الأرضي على بروز عظمي\n3. سجل أثناء انقباضات العضلة"
                },
                circuitTemplate: 'emg-amplifier',
                frequency: 150,
                amplitude: 0.5
            },
            eeg: {
                name: {
                    en: "EEG - Electroencephalography",
                    ar: "تخطيط الدماغ الكهربائي"
                },
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: {
                    en: "Measures electrical activity of the brain from the scalp.",
                    ar: "يقيس النشاط الكهربائي للدماغ من فروة الرأس."
                },
                procedure: {
                    en: "1. Place electrodes according to 10-20 system\n2. Ensure impedance < 5kΩ\n3. Record with eyes open/closed",
                    ar: "1. ضع الأقطاب وفقاً لنظام 10-20\n2. تأكد من أن المقاومة < 5kΩ\n3. سجل مع العينين مفتوحتين/مغلقتين"
                },
                circuitTemplate: 'bandpass-filter',
                frequency: 10,
                amplitude: 0.1
            },
            eog: {
                name: {
                    en: "EOG - Electrooculography",
                    ar: "تخطيط العين الكهربائي"
                },
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4', 'IN5'],
                description: {
                    en: "Measures eye movements based on corneal-retinal potential difference.",
                    ar: "يقيس حركات العين بناءً على فرق الجهد بين القرنية والشبكية."
                },
                procedure: {
                    en: "1. Place electrodes around eyes\n2. Place ground on forehead\n3. Ask subject to follow target",
                    ar: "1. ضع الأقطاب حول العينين\n2. ضع القطب الأرضي على الجبهة\n3. اطلب من المريض تتبع الهدف"
                },
                circuitTemplate: 'rc-highpass',
                frequency: 0.5,
                amplitude: 0.8
            },
            bp: {
                name: {
                    en: "Blood Pressure Monitor",
                    ar: "جهاز قياس ضغط الدم"
                },
                requiredSensors: ['IN6'],
                description: {
                    en: "Oscillometric blood pressure measurement using pressure cuff.",
                    ar: "قياس ضغط الدم بالطريقة التذبذبية باستخدام كفة الضغط."
                },
                procedure: {
                    en: "1. Wrap cuff around upper arm\n2. Inflate above systolic pressure\n3. Slowly deflate and record oscillations",
                    ar: "1. لف الكفة حول الذراع العلوي\n2. انفخ فوق الضغط الانقباضي\n3. أفرغ ببطء وسجل التذبذبات"
                },
                circuitTemplate: 'rc-lowpass',
                frequency: 1.0,
                amplitude: 2.0
            },
            impedance: {
                name: {
                    en: "Body Impedance Analyzer",
                    ar: "محلل مقاومة الجسم"
                },
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: {
                    en: "Measures body composition using bioelectrical impedance analysis.",
                    ar: "يقيس تركيب الجسم باستخدام تحليل المقاومة الكهربائية الحيوية."
                },
                procedure: {
                    en: "1. Connect four electrodes\n2. Apply safe AC current\n3. Measure voltage drop\n4. Calculate impedance",
                    ar: "1. وصل أربعة أقطاب\n2. طبق تيار متردد آمن\n3. قس انخفاض الجهد\n4. احسب المقاومة"
                },
                circuitTemplate: 'non-inverting-amp',
                frequency: 1000,
                amplitude: 0.001
            }
        };

        // Circuit Templates with PCB Component Mappings
        const CircuitTemplates = {
            'rc-lowpass': {
                name: 'RC Low-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 1000, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'resistor', x: 250, y: 150, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'capacitor', x: 400, y: 150, id: 'c1', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'scope1-in' }
                ]
            },
            'rc-highpass': {
                name: 'RC High-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 100, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'capacitor', x: 250, y: 150, id: 'c1', properties: { capacitance: 1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 400, y: 150, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'non-inverting-amp': {
                name: 'Non-inverting Op-Amp Amplifier',
                components: [
                    { type: 'function-generator', x: 100, y: 200, id: 'fg1', properties: { frequency: 1000, amplitude: 0.1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'op-amp', x: 300, y: 200, id: 'op1', properties: { gain: 10 }, footprint: 'SOIC8' },
                    { type: 'resistor', x: 250, y: 250, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'resistor', x: 350, y: 250, id: 'r2', properties: { resistance: 9000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'op1-in+' },
                    { from: 'op1-in-', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'op1-out' },
                    { from: 'op1-out', to: 'scope1-in' }
                ]
            },
            'bandpass-filter': {
                name: 'Band-Pass Filter (HPF + LPF)',
                components: [
                    { type: 'function-generator', x: 80, y: 200, id: 'fg1', properties: { frequency: 100, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'capacitor', x: 200, y: 200, id: 'c1', properties: { capacitance: 1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 320, y: 200, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'resistor', x: 440, y: 200, id: 'r2', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'capacitor', x: 560, y: 200, id: 'c2', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 680, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            },
            'ecg-frontend': {
                name: 'ECG Frontend Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'ecg1', properties: { signalType: 'ECG', amplitude: 0.001 }, footprint: 'CONN_3PIN' },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 1000 }, footprint: 'SOIC8' },
                    { type: 'capacitor', x: 360, y: 200, id: 'c1', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 480, y: 200, id: 'r1', properties: { resistance: 10000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 620, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'ecg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'emg-amplifier': {
                name: 'EMG Amplifier Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'emg1', properties: { signalType: 'EMG', amplitude: 0.0001 }, footprint: 'CONN_3PIN' },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 10000 }, footprint: 'SOIC8' },
                    { type: 'capacitor', x: 360, y: 180, id: 'c1', properties: { capacitance: 0.01 }, footprint: 'C_0805' },
                    { type: 'capacitor', x: 360, y: 220, id: 'c2', properties: { capacitance: 10 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'emg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            }
        };

        // PCB Component Footprints
        const ComponentFootprints = {
            'R_0805': { width: 2.0, height: 1.25, pads: [{ x: -0.95, y: 0 }, { x: 0.95, y: 0 }], color: '#2c2c2c' },
            'C_0805': { width: 2.0, height: 1.25, pads: [{ x: -0.95, y: 0 }, { x: 0.95, y: 0 }], color: '#8b4513' },
            'SOIC8': { width: 5.0, height: 4.0, pads: [
                { x: -1.905, y: -1.27 }, { x: -1.905, y: -0.635 }, { x: -1.905, y: 0.635 }, { x: -1.905, y: 1.27 },
                { x: 1.905, y: 1.27 }, { x: 1.905, y: 0.635 }, { x: 1.905, y: -0.635 }, { x: 1.905, y: -1.27 }
            ], color: '#1a1a1a' },
            'SMA_CONNECTOR': { width: 6.0, height: 6.0, pads: [{ x: 0, y: 0 }], color: '#c0c0c0' },
            'CONN_3PIN': { width: 7.5, height: 2.5, pads: [{ x: -2.54, y: 0 }, { x: 0, y: 0 }, { x: 2.54, y: 0 }], color: '#333333' }
        };

        // Language Management Functions
        function toggleLanguage() {
            console.log('🔄 Language toggle initiated...');

            try {
                const langBtn = document.getElementById('langToggle');
                if (!langBtn) {
                    console.error('❌ Language toggle button not found!');
                    return;
                }

                langBtn.classList.add('switching');

                // Toggle language
                const previousLang = AppState.currentLanguage;
                AppState.currentLanguage = AppState.currentLanguage === 'en' ? 'ar' : 'en';
                console.log(`🔄 Language changed from ${previousLang} to ${AppState.currentLanguage}`);

                // Update document direction and language
                document.documentElement.setAttribute('data-lang', AppState.currentLanguage);
                document.documentElement.setAttribute('dir', AppState.currentLanguage === 'ar' ? 'rtl' : 'ltr');
                document.documentElement.setAttribute('lang', AppState.currentLanguage);

                // Add/remove RTL class
                document.body.classList.toggle('rtl', AppState.currentLanguage === 'ar');

                // Update all translatable elements
                updateAllTranslations();

                // Update language button
                const langText = langBtn.querySelector('.lang-text');
                if (langText && Translations[AppState.currentLanguage]) {
                    langText.textContent = Translations[AppState.currentLanguage].language;
                    console.log('✅ Language button text updated');
                } else {
                    console.warn('⚠️ Could not update language button text');
                }

                // Update module content if module is selected
                if (AppState.selectedModule) {
                    updateModuleContent();
                }

                // Remove animation class after animation completes
                setTimeout(() => {
                    langBtn.classList.remove('switching');
                }, 600);

                // Save language preference
                localStorage.setItem('bioSignalExplorerLang', AppState.currentLanguage);

                console.log(`🌐 Language successfully switched to: ${AppState.currentLanguage === 'en' ? 'English' : 'العربية'}`);

            } catch (error) {
                console.error('❌ Error during language toggle:', error);
            }
        }

        function updateAllTranslations() {
            console.log('🔄 Updating all translations...');

            try {
                // Update all elements with data-en and data-ar attributes
                const translatableElements = document.querySelectorAll('[data-en][data-ar]');
                console.log(`📝 Found ${translatableElements.length} translatable elements`);

                translatableElements.forEach((element, index) => {
                    const currentLang = AppState.currentLanguage;
                    const translation = element.getAttribute(`data-${currentLang}`);

                    if (translation) {
                        element.textContent = translation;
                    } else {
                        console.warn(`⚠️ No translation found for element ${index} in language ${currentLang}`);
                    }

                    // Update title attributes for tooltips
                    const titleKey = `title-${currentLang}`;
                    const titleTranslation = element.getAttribute(titleKey);
                    if (titleTranslation) {
                        element.setAttribute('title', titleTranslation);
                    }
                });

                // Update select options
                const selectElements = document.querySelectorAll('select option[data-en][data-ar]');
                console.log(`📋 Found ${selectElements.length} translatable select options`);

                selectElements.forEach(option => {
                    const currentLang = AppState.currentLanguage;
                    const translation = option.getAttribute(`data-${currentLang}`);
                    if (translation) {
                        option.textContent = translation;
                    }
                });

                // Update dynamic content
                updateTabContent();
                updateSensorGrid();

                // Update module-specific content if a module is selected
                if (AppState.selectedModule) {
                    updateModuleContent();
                }

                console.log('✅ All translations updated successfully');

            } catch (error) {
                console.error('❌ Error updating translations:', error);
            }
        }

        function updateModuleContent() {
            if (!AppState.selectedModule) return;

            const module = LabModules[AppState.selectedModule];
            const currentLang = AppState.currentLanguage;

            // Update LCD display
            const lcdText = document.getElementById('virtualLCD')?.querySelector('.lcd-text');
            if (lcdText && AppState.powerOn) {
                const moduleName = typeof module.name === 'object' ? module.name[currentLang] : module.name;
                const connectText = currentLang === 'en' ? 'CONNECT SENSORS TO BEGIN' : 'قم بتوصيل أجهزة الاستشعار للبدء';
                lcdText.innerHTML = `> ${currentLang === 'en' ? 'MODULE' : 'الوحدة'}: ${moduleName}<br>> ${connectText}<br>> ${currentLang === 'en' ? 'FREQ' : 'التردد'}: ${module.frequency}Hz ${currentLang === 'en' ? 'AMP' : 'السعة'}: ${module.amplitude}V`;
            }
        }

        function initializeLanguage() {
            console.log('🌐 Initializing language system...');

            // Load saved language preference
            const savedLang = localStorage.getItem('bioSignalExplorerLang');
            if (savedLang && (savedLang === 'en' || savedLang === 'ar')) {
                AppState.currentLanguage = savedLang;
                console.log('📱 Loaded saved language:', savedLang);
            } else {
                console.log('🔧 Using default language: en');
            }

            // Set initial document attributes
            document.documentElement.setAttribute('data-lang', AppState.currentLanguage);
            document.documentElement.setAttribute('dir', AppState.currentLanguage === 'ar' ? 'rtl' : 'ltr');
            document.documentElement.setAttribute('lang', AppState.currentLanguage);
            document.body.classList.toggle('rtl', AppState.currentLanguage === 'ar');

            // Update language button with retry mechanism
            setTimeout(() => {
                const langBtn = document.getElementById('langToggle');
                if (langBtn) {
                    const langText = langBtn.querySelector('.lang-text');
                    if (langText && Translations[AppState.currentLanguage]) {
                        langText.textContent = Translations[AppState.currentLanguage].language;
                        console.log('✅ Language button updated successfully');
                    } else {
                        console.warn('⚠️ Language button text element not found');
                    }
                } else {
                    console.warn('⚠️ Language button not found');
                }

                // Update all translations
                updateAllTranslations();
                console.log('🔄 All translations updated');
            }, 50);
        }

        // Mode Management Functions
        function switchMode(mode) {
            AppState.currentMode = mode;

            // Update button states
            document.getElementById('simModeBtn').classList.toggle('active', mode === 'simulation');
            document.getElementById('designModeBtn').classList.toggle('active', mode === 'design');
            document.getElementById('pcbModeBtn').classList.toggle('active', mode === 'pcb');

            // Show/hide views
            document.getElementById('simulationView').classList.toggle('active', mode === 'simulation');
            document.getElementById('designView').classList.toggle('active', mode === 'design');
            document.getElementById('pcbView').classList.toggle('active', mode === 'pcb');

            // Initialize canvas based on mode
            if (mode === 'design') {
                initializeCircuitCanvas();
            } else if (mode === 'pcb') {
                initializePCBCanvas();
                update3DPreview();
            }
        }

        // Simulation Lab Functions
        function togglePower() {
            AppState.powerOn = !AppState.powerOn;
            const powerBtn = document.getElementById('powerBtn');
            const statusPanel = document.getElementById('statusPanel');
            const virtualLCD = document.getElementById('virtualLCD');

            const currentLang = AppState.currentLanguage;
            const translations = Translations[currentLang];

            if (AppState.powerOn) {
                powerBtn.innerHTML = `<span data-en="🔴 Power Off" data-ar="🔴 إيقاف النظام">${translations.powerOff}</span>`;
                powerBtn.className = 'btn btn-danger';
                statusPanel.className = 'status-indicator status-online';
                statusPanel.innerHTML = `<span class="status-dot"></span><span data-en="${Translations.en.systemOnline}" data-ar="${Translations.ar.systemOnline}">${translations.systemOnline}</span>`;

                // Enhanced LCD display
                const lcdText = virtualLCD.querySelector('.lcd-text');
                const systemOnlineText = currentLang === 'en' ? 'SYSTEM ONLINE' : 'النظام يعمل';
                const selectModuleText = currentLang === 'en' ? 'SELECT MODULE TO BEGIN' : 'اختر الوحدة للبدء';
                const statusText = currentLang === 'en' ? 'STATUS: READY' : 'الحالة: جاهز';
                lcdText.innerHTML = `> ${systemOnlineText}<br>> ${selectModuleText}<br>> ${statusText}`;

                // Enable controls
                document.getElementById('moduleSelect').disabled = false;
                document.getElementById('analysisBtn').disabled = false;

                // Start LCD animation
                startLCDAnimation();
            } else {
                powerBtn.innerHTML = `<span data-en="⚡ Power On" data-ar="⚡ تشغيل النظام">${translations.powerOn}</span>`;
                powerBtn.className = 'btn btn-success';
                statusPanel.className = 'status-indicator status-offline';
                statusPanel.innerHTML = `<span class="status-dot"></span><span data-en="${Translations.en.systemOffline}" data-ar="${Translations.ar.systemOffline}">${translations.systemOffline}</span>`;

                const lcdText = virtualLCD.querySelector('.lcd-text');
                const systemOfflineText = currentLang === 'en' ? 'SYSTEM OFFLINE' : 'النظام مغلق';
                const powerOnText = currentLang === 'en' ? 'POWER ON TO START' : 'شغل النظام للبدء';
                lcdText.innerHTML = `> ${systemOfflineText}<br>> ${powerOnText}`;

                // Disable controls
                document.getElementById('moduleSelect').disabled = true;
                document.getElementById('moduleSelect').value = '';
                document.getElementById('analysisBtn').disabled = true;
                document.getElementById('viewCircuitBtn').disabled = true;
                AppState.selectedModule = null;

                // Clear oscilloscope
                clearOscilloscope();
                stopLCDAnimation();
            }

            updateSensorGrid();
            updateTabContent();
        }

        function selectModule() {
            const moduleSelect = document.getElementById('moduleSelect');
            const selectedValue = moduleSelect.value;

            if (selectedValue && LabModules[selectedValue]) {
                AppState.selectedModule = selectedValue;
                document.getElementById('viewCircuitBtn').disabled = false;

                const module = LabModules[selectedValue];
                const currentLang = AppState.currentLanguage;
                const lcdText = document.getElementById('virtualLCD').querySelector('.lcd-text');

                const moduleName = typeof module.name === 'object' ? module.name[currentLang] : module.name;
                const moduleText = currentLang === 'en' ? 'MODULE' : 'الوحدة';
                const connectText = currentLang === 'en' ? 'CONNECT SENSORS TO BEGIN' : 'قم بتوصيل أجهزة الاستشعار للبدء';
                const freqText = currentLang === 'en' ? 'FREQ' : 'التردد';
                const ampText = currentLang === 'en' ? 'AMP' : 'السعة';

                lcdText.innerHTML = `> ${moduleText}: ${moduleName}<br>> ${connectText}<br>> ${freqText}: ${module.frequency}Hz ${ampText}: ${module.amplitude}V`;

                updateSensorGrid();
                updateTabContent();
                startOscilloscopeAnimation();
                updateLCDStatus();
            } else {
                AppState.selectedModule = null;
                document.getElementById('viewCircuitBtn').disabled = true;
                clearOscilloscope();
            }
        }

        function updateSensorGrid() {
            const sensorGrid = document.getElementById('sensorGrid');
            const sensors = ['IN1', 'IN2', 'IN3', 'IN4', 'IN5', 'IN6', 'IN7'];

            sensorGrid.innerHTML = '';

            sensors.forEach(sensor => {
                const sensorDiv = document.createElement('div');
                sensorDiv.style.cssText = 'display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: var(--bg-secondary); border-radius: 6px; border: 1px solid var(--border-color);';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = sensor;
                checkbox.disabled = !AppState.powerOn;

                const label = document.createElement('label');
                label.textContent = sensor;
                label.style.cssText = 'flex: 1; font-size: 0.85rem; cursor: pointer;';
                label.setAttribute('for', sensor);

                const led = document.createElement('div');
                led.style.cssText = 'width: 8px; height: 8px; border-radius: 50%; background: var(--border-color); transition: all 0.3s ease;';

                // Check if sensor is required for current module
                if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                    const requiredSensors = LabModules[AppState.selectedModule].requiredSensors;
                    if (requiredSensors.includes(sensor)) {
                        checkbox.addEventListener('change', function() {
                            if (this.checked) {
                                led.style.background = 'var(--accent-green)';
                                led.style.boxShadow = '0 0 8px var(--accent-green)';
                            } else {
                                led.style.background = 'var(--border-color)';
                                led.style.boxShadow = 'none';
                            }
                        });
                    }
                }

                sensorDiv.appendChild(checkbox);
                sensorDiv.appendChild(label);
                sensorDiv.appendChild(led);
                sensorGrid.appendChild(sensorDiv);
            });
        }

        function updateFrequency() {
            const freqSlider = document.getElementById('freqSlider');
            const freqValue = document.getElementById('freqValue');
            freqValue.textContent = freqSlider.value;
        }

        function updateAmplitude() {
            const ampSlider = document.getElementById('ampSlider');
            const ampValue = document.getElementById('ampValue');
            ampValue.textContent = parseFloat(ampSlider.value).toFixed(1);
        }

        function toggleAnalysisMode() {
            AppState.analysisMode = !AppState.analysisMode;
            const analysisBtn = document.getElementById('analysisBtn');
            const currentLang = AppState.currentLanguage;

            if (AppState.analysisMode) {
                const exitAnalysisText = currentLang === 'en' ? '📊 Exit Analysis Mode' : '📊 خروج من وضع التحليل';
                analysisBtn.innerHTML = `<span data-en="📊 Exit Analysis Mode" data-ar="📊 خروج من وضع التحليل">${exitAnalysisText}</span>`;
                analysisBtn.className = 'btn btn-secondary';
            } else {
                const enterAnalysisText = currentLang === 'en' ? '📊 Enter Circuit Analysis' : '📊 دخول وضع التحليل';
                analysisBtn.innerHTML = `<span data-en="📊 Enter Circuit Analysis" data-ar="📊 دخول وضع التحليل">${enterAnalysisText}</span>`;
                analysisBtn.className = 'btn btn-warning';
            }
        }

        function viewInternalCircuit() {
            if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                const module = LabModules[AppState.selectedModule];
                switchMode('design');
                loadTemplate(module.circuitTemplate);
            }
        }

        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';

            updateTabContent(tabName);
        }

        function updateTabContent(activeTab = 'info') {
            const tabContent = document.getElementById('tabContent');
            const currentLang = AppState.currentLanguage;
            const translations = Translations[currentLang];

            if (!AppState.selectedModule) {
                const selectModuleText = currentLang === 'en' ? 'Select a measurement module to begin' : 'اختر وحدة قياس للبدء';
                const professionalText = currentLang === 'en' ? 'Professional Biomedical Instrumentation' : 'أجهزة طبية حيوية احترافية';
                const powerOnText = currentLang === 'en' ? 'Power On' : 'تشغيل';
                const selectModuleStepText = currentLang === 'en' ? 'Select Module' : 'اختر الوحدة';
                const connectSensorsText = currentLang === 'en' ? 'Connect Sensors' : 'وصل أجهزة الاستشعار';
                const measureSignalText = currentLang === 'en' ? 'Measure Signal' : 'قياس الإشارة';

                tabContent.innerHTML = `
                    <div class="welcome-content">
                        <div class="demo-photo">
                            <div class="demo-photo-content">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🔬</div>
                                <div>${selectModuleText}</div>
                                <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 0.5rem;">${professionalText}</div>
                            </div>
                        </div>

                        <div class="block-diagram">
                            <div class="block">
                                <span class="block-icon animated-icon">⚡</span>
                                <div>${powerOnText}</div>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block">
                                <span class="block-icon">🔧</span>
                                <div>${selectModuleStepText}</div>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block">
                                <span class="block-icon">🔌</span>
                                <div>${connectSensorsText}</div>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block">
                                <span class="block-icon heartbeat-icon">📊</span>
                                <div>${measureSignalText}</div>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const module = LabModules[AppState.selectedModule];
            const moduleName = typeof module.name === 'object' ? module.name[currentLang] : module.name;
            const moduleDescription = typeof module.description === 'object' ? module.description[currentLang] : module.description;
            const moduleProcedure = typeof module.procedure === 'object' ? module.procedure[currentLang] : module.procedure;

            switch (activeTab) {
                case 'info':
                    const realtimeText = currentLang === 'en' ? 'Real-time' : 'مراقبة في الوقت الفعلي';
                    const monitoringText = currentLang === 'en' ? 'Monitoring' : 'للمراقبة';
                    const professionalGradeText = currentLang === 'en' ? 'Professional Grade Instrumentation' : 'أجهزة قياس احترافية';
                    const requiredSensorsText = currentLang === 'en' ? 'Required Sensors:' : 'أجهزة الاستشعار المطلوبة:';
                    const sensorInputText = currentLang === 'en' ? 'Sensor Input' : 'دخل الاستشعار';
                    const amplificationText = currentLang === 'en' ? 'Amplification' : 'التضخيم';
                    const filteringText = currentLang === 'en' ? 'Filtering' : 'الترشيح';
                    const displayText = currentLang === 'en' ? 'Display' : 'العرض';

                    tabContent.innerHTML = `
                        <div class="fade-in-up">
                            <h4 style="color: var(--accent-cyan); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                <span class="heartbeat-icon">${getModuleIcon(AppState.selectedModule)}</span>
                                ${moduleName}
                            </h4>

                            <div class="demo-photo" style="margin-bottom: 1rem;">
                                <div class="demo-photo-content">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">${getModuleIcon(AppState.selectedModule)}</div>
                                    <div>${realtimeText} ${moduleName.split(' ')[0]} ${monitoringText}</div>
                                    <div style="font-size: 0.8rem; opacity: 0.8; margin-top: 0.5rem;">${professionalGradeText}</div>
                                </div>
                            </div>

                            <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 1rem;">${moduleDescription}</p>

                            <div class="interactive-control">
                                <strong style="color: var(--accent-green);">${requiredSensorsText}</strong> ${module.requiredSensors.join(', ')}
                            </div>

                            <div class="block-diagram">
                                <div class="block">
                                    <span class="block-icon">🔌</span>
                                    <div>${sensorInputText}</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon">📡</span>
                                    <div>${amplificationText}</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon">🔧</span>
                                    <div>${filteringText}</div>
                                </div>
                                <div class="block-arrow">→</div>
                                <div class="block">
                                    <span class="block-icon animated-icon">📊</span>
                                    <div>${displayText}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'procedure':
                    const measurementProcedureText = currentLang === 'en' ? '📝 Measurement Procedure' : '📝 إجراءات القياس';
                    const interactiveCircuitText = currentLang === 'en' ? 'Interactive Circuit Diagram' : 'مخطط الدائرة التفاعلي';
                    const viewInternalText = currentLang === 'en' ? 'Click "View Internal Circuit" to explore' : 'اضغط "عرض الدائرة الداخلية" للاستكشاف';

                    tabContent.innerHTML = `
                        <div class="fade-in-up">
                            <h4 style="color: var(--accent-cyan); margin-bottom: 1rem;">${measurementProcedureText}</h4>

                            <div class="circuit-diagram" style="margin-bottom: 1rem;">
                                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                                    <div style="font-size: 2rem; margin-bottom: 1rem;">${getModuleIcon(AppState.selectedModule)}</div>
                                    <div>${interactiveCircuitText}</div>
                                    <div style="font-size: 0.8rem; margin-top: 0.5rem;">${viewInternalText}</div>
                                </div>
                            </div>

                            <div class="interactive-control">
                                <pre style="color: var(--text-secondary); line-height: 1.8; white-space: pre-wrap; font-family: inherit;">${moduleProcedure}</pre>
                            </div>

                            <div class="demo-waveform" style="margin-top: 1rem;">
                                <canvas id="procedureWaveform" style="width: 100%; height: 100%;"></canvas>
                            </div>
                        </div>
                    `;
                    startProcedureWaveform();
                    break;
                case 'export':
                    const dataExportText = currentLang === 'en' ? '💾 Data Export & Analysis' : '💾 تصدير وتحليل البيانات';
                    const exportDescText = currentLang === 'en' ? 'Export measurement data for offline analysis and research:' : 'تصدير بيانات القياس للتحليل والبحث خارج الخط:';
                    const exportCSVText = currentLang === 'en' ? '📊 Export CSV Data' : '📊 تصدير بيانات CSV';
                    const exportMATLABText = currentLang === 'en' ? '🔬 Export MATLAB Format' : '🔬 تصدير تنسيق MATLAB';
                    const exportPythonText = currentLang === 'en' ? '🐍 Export Python Script' : '🐍 تصدير سكريبت Python';
                    const sampleRateText = currentLang === 'en' ? 'Sample Rate:' : 'معدل العينة:';
                    const durationText = currentLang === 'en' ? 'Duration:' : 'المدة:';
                    const dataPointsText = currentLang === 'en' ? 'Data Points:' : 'نقاط البيانات:';
                    const formatText = currentLang === 'en' ? 'Format:' : 'التنسيق:';
                    const timeVoltageText = currentLang === 'en' ? 'Time (s), Voltage (V)' : 'الوقت (ثانية)، الجهد (فولت)';

                    tabContent.innerHTML = `
                        <div class="fade-in-up">
                            <h4 style="color: var(--accent-cyan); margin-bottom: 1rem;">${dataExportText}</h4>

                            <div class="interactive-control">
                                <p style="color: var(--text-secondary); margin-bottom: 1rem;">${exportDescText}</p>

                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                                    <button type="button" class="btn btn-primary" onclick="exportData('csv')" style="width: 100%;">
                                        ${exportCSVText}
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="exportData('matlab')" style="width: 100%;">
                                        ${exportMATLABText}
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="exportData('python')" style="width: 100%;">
                                        ${exportPythonText}
                                    </button>
                                </div>
                            </div>

                            <div class="demo-waveform">
                                <canvas id="exportPreview" style="width: 100%; height: 100%;"></canvas>
                            </div>

                            <div style="margin-top: 1rem; padding: 1rem; background: var(--panel-bg); border-radius: 8px; font-family: 'Roboto Mono', monospace; font-size: 0.85rem; color: var(--text-secondary);">
                                <div><strong>${sampleRateText}</strong> 1000 Hz</div>
                                <div><strong>${durationText}</strong> 10 ${currentLang === 'en' ? 'seconds' : 'ثواني'}</div>
                                <div><strong>${dataPointsText}</strong> 10,000</div>
                                <div><strong>${formatText}</strong> ${timeVoltageText}</div>
                            </div>
                        </div>
                    `;
                    startExportPreview();
                    break;
            }
        }

        function getModuleIcon(moduleType) {
            const icons = {
                'ecg': '❤️',
                'emg': '💪',
                'eeg': '🧠',
                'eog': '👁️',
                'bp': '🩺',
                'impedance': '⚡'
            };
            return icons[moduleType] || '🔬';
        }

        // Circuit Design Toolkit Functions
        function initializeCircuitCanvas() {
            const canvas = document.getElementById('circuitCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add event listeners
            canvas.addEventListener('click', handleCanvasClick);
            canvas.addEventListener('mousedown', handleCanvasMouseDown);
            canvas.addEventListener('mousemove', handleCanvasMouseMove);
            canvas.addEventListener('mouseup', handleCanvasMouseUp);

            updateCircuitInfo();
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const allDropdowns = document.querySelectorAll('.dropdown');

            // Close other dropdowns
            allDropdowns.forEach(d => {
                if (d.id !== dropdownId) {
                    d.classList.remove('active');
                }
            });

            dropdown.classList.toggle('active');
        }

        function selectComponent(componentType) {
            AppState.circuitSimulation.selectedComponent = componentType;
            document.getElementById('componentDropdown').classList.remove('active');

            // Change cursor to indicate component selection
            const canvas = document.getElementById('circuitCanvas');
            if (canvas) canvas.style.cursor = 'crosshair';
        }

        function loadTemplate(templateName) {
            if (CircuitTemplates[templateName]) {
                const template = CircuitTemplates[templateName];
                AppState.circuitSimulation.components = [...template.components];
                AppState.circuitSimulation.connections = [...template.connections];
                AppState.circuitSimulation.readyForPCB = true;

                drawCircuitCanvas();
                updateCircuitInfo();
                updateProceedToPCBButton();

                document.getElementById('templateDropdown').classList.remove('active');
            }
        }

        function proceedToPCB() {
            if (AppState.circuitSimulation.readyForPCB && AppState.circuitSimulation.components.length > 0) {
                // Transfer circuit data to PCB workbench
                transferCircuitToPCB();
                switchMode('pcb');
            }
        }

        function transferCircuitToPCB() {
            // Clear existing PCB data
            AppState.pcbWorkbench.components = [];
            AppState.pcbWorkbench.ratsnest = [];

            // Convert circuit components to PCB components
            AppState.circuitSimulation.components.forEach(comp => {
                if (comp.footprint) {
                    AppState.pcbWorkbench.components.push({
                        id: comp.id,
                        type: comp.type,
                        footprint: comp.footprint,
                        x: 0, // Will be placed by user
                        y: 0,
                        rotation: 0,
                        placed: false,
                        properties: comp.properties
                    });
                }
            });

            // Generate ratsnest from connections
            AppState.circuitSimulation.connections.forEach(conn => {
                AppState.pcbWorkbench.ratsnest.push({
                    from: conn.from,
                    to: conn.to,
                    routed: false
                });
            });

            // Populate component bin
            populateComponentBin();
            updatePCBInfo();
        }

        // PCB Workbench Functions
        function initializePCBCanvas() {
            const canvas = document.getElementById('pcbCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Add event listeners
            canvas.addEventListener('click', handlePCBCanvasClick);
            canvas.addEventListener('mousedown', handlePCBCanvasMouseDown);
            canvas.addEventListener('mousemove', handlePCBCanvasMouseMove);
            canvas.addEventListener('mouseup', handlePCBCanvasMouseUp);

            drawPCBCanvas();
        }

        function populateComponentBin() {
            const componentBin = document.getElementById('componentBin');
            componentBin.innerHTML = '';

            AppState.pcbWorkbench.components.forEach(comp => {
                if (!comp.placed) {
                    const item = document.createElement('div');
                    item.className = 'component-bin-item';
                    item.draggable = true;
                    item.dataset.componentId = comp.id;

                    const icon = getComponentIcon(comp.type);
                    const footprint = ComponentFootprints[comp.footprint];

                    item.innerHTML = `
                        <span style="font-size: 1.2rem;">${icon}</span>
                        <div>
                            <div style="font-weight: 600; font-size: 0.9rem;">${comp.id}</div>
                            <div style="font-size: 0.75rem; color: var(--text-muted);">${comp.footprint}</div>
                        </div>
                    `;

                    item.addEventListener('dragstart', handleComponentDragStart);
                    componentBin.appendChild(item);
                }
            });
        }

        function getComponentIcon(type) {
            const icons = {
                'resistor': '🔧',
                'capacitor': '⚡',
                'op-amp': '🔺',
                'function-generator': '📡',
                'bio-signal': '💓',
                'oscilloscope': '📊'
            };
            return icons[type] || '🔲';
        }

        function handleComponentDragStart(event) {
            event.dataTransfer.setData('text/plain', event.target.dataset.componentId);
            event.target.classList.add('dragging');
        }

        function handlePCBCanvasClick(event) {
            const canvas = event.target;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            const gridSize = parseFloat(document.getElementById('gridSize').value) * 96; // Convert inches to pixels (96 DPI)
            const gridX = Math.round(x / gridSize) * gridSize;
            const gridY = Math.round(y / gridSize) * gridSize;

            switch (AppState.pcbWorkbench.selectedTool) {
                case 'select':
                    handleSelectTool(gridX, gridY);
                    break;
                case 'route':
                    handleRouteTool(gridX, gridY);
                    break;
                case 'via':
                    handleViaTool(gridX, gridY);
                    break;
            }
        }

        function handleSelectTool(x, y) {
            // Find component at position
            const component = findPCBComponentAt(x, y);
            if (component) {
                // Select component for editing
                showPCBComponentProperties(component);
            }
        }

        function handleRouteTool(x, y) {
            // Implement trace routing logic
            console.log('Route tool clicked at:', x, y);
        }

        function handleViaTool(x, y) {
            // Place via
            AppState.pcbWorkbench.vias.push({
                x: x,
                y: y,
                id: `via_${Date.now()}`
            });

            drawPCBCanvas();
            updatePCBInfo();
        }

        function findPCBComponentAt(x, y) {
            return AppState.pcbWorkbench.components.find(comp => {
                if (!comp.placed) return false;

                const footprint = ComponentFootprints[comp.footprint];
                const halfWidth = (footprint.width * 9.6) / 2; // Convert mm to pixels
                const halfHeight = (footprint.height * 9.6) / 2;

                return x >= comp.x - halfWidth && x <= comp.x + halfWidth &&
                       y >= comp.y - halfHeight && y <= comp.y + halfHeight;
            });
        }

        function selectPCBTool(tool) {
            AppState.pcbWorkbench.selectedTool = tool;

            // Update tool button states
            document.querySelectorAll('#selectTool, #routeTool, #viaTool').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
            });

            const toolBtn = document.getElementById(tool + 'Tool');
            if (toolBtn) {
                toolBtn.classList.remove('btn-secondary');
                toolBtn.classList.add('btn-primary');
            }
        }

        function drawPCBCanvas() {
            const canvas = document.getElementById('pcbCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Clear canvas with PCB green
            ctx.fillStyle = 'var(--pcb-green)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw grid
            drawPCBGrid(ctx, canvas.width, canvas.height);

            // Draw board outline
            drawBoardOutline(ctx, canvas.width, canvas.height);

            // Draw ratsnest (if layer is visible)
            if (document.getElementById('ratsnestLayer').checked) {
                drawRatsnest(ctx);
            }

            // Draw placed components
            drawPCBComponents(ctx);

            // Draw traces
            if (document.getElementById('topCopperLayer').checked) {
                drawTraces(ctx, 'top');
            }
            if (document.getElementById('bottomCopperLayer').checked) {
                drawTraces(ctx, 'bottom');
            }

            // Draw vias
            if (document.getElementById('drillLayer').checked) {
                drawVias(ctx);
            }
        }

        function drawPCBGrid(ctx, width, height) {
            const gridSize = parseFloat(document.getElementById('gridSize').value) * 96; // Convert inches to pixels

            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Vertical lines
            for (let x = 0; x <= width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Horizontal lines
            for (let y = 0; y <= height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        }

        function drawBoardOutline(ctx, width, height) {
            const margin = 50;
            ctx.strokeStyle = 'var(--accent-yellow)';
            ctx.lineWidth = 2;
            ctx.strokeRect(margin, margin, width - 2 * margin, height - 2 * margin);
        }

        function drawRatsnest(ctx) {
            ctx.strokeStyle = 'var(--ratsnest-color)';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);

            AppState.pcbWorkbench.ratsnest.forEach(net => {
                if (!net.routed) {
                    const fromComp = AppState.pcbWorkbench.components.find(c => net.from.startsWith(c.id));
                    const toComp = AppState.pcbWorkbench.components.find(c => net.to.startsWith(c.id));

                    if (fromComp && toComp && fromComp.placed && toComp.placed) {
                        ctx.beginPath();
                        ctx.moveTo(fromComp.x, fromComp.y);
                        ctx.lineTo(toComp.x, toComp.y);
                        ctx.stroke();
                    }
                }
            });

            ctx.setLineDash([]);
        }

        function drawPCBComponents(ctx) {
            AppState.pcbWorkbench.components.forEach(comp => {
                if (comp.placed) {
                    drawPCBComponent(ctx, comp);
                }
            });
        }

        function drawPCBComponent(ctx, comp) {
            const footprint = ComponentFootprints[comp.footprint];
            if (!footprint) return;

            ctx.save();
            ctx.translate(comp.x, comp.y);
            ctx.rotate(comp.rotation * Math.PI / 180);

            // Draw component body
            const width = footprint.width * 9.6; // Convert mm to pixels
            const height = footprint.height * 9.6;

            ctx.fillStyle = footprint.color;
            ctx.fillRect(-width/2, -height/2, width, height);

            // Draw pads
            ctx.fillStyle = 'var(--copper-color)';
            footprint.pads.forEach(pad => {
                const padSize = 1.5 * 9.6; // 1.5mm pad size
                ctx.fillRect(pad.x * 9.6 - padSize/2, pad.y * 9.6 - padSize/2, padSize, padSize);
            });

            // Draw component label
            if (document.getElementById('silkscreenLayer').checked) {
                ctx.fillStyle = 'white';
                ctx.font = '10px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(comp.id, 0, height/2 + 15);
            }

            ctx.restore();
        }

        function drawTraces(ctx, layer) {
            ctx.strokeStyle = layer === 'top' ? 'var(--trace-color)' : 'var(--copper-color)';
            ctx.lineWidth = 3;

            AppState.pcbWorkbench.traces.forEach(trace => {
                if (trace.layer === layer) {
                    ctx.beginPath();
                    ctx.moveTo(trace.points[0].x, trace.points[0].y);
                    for (let i = 1; i < trace.points.length; i++) {
                        ctx.lineTo(trace.points[i].x, trace.points[i].y);
                    }
                    ctx.stroke();
                }
            });
        }

        function drawVias(ctx) {
            ctx.fillStyle = 'var(--via-color)';

            AppState.pcbWorkbench.vias.forEach(via => {
                ctx.beginPath();
                ctx.arc(via.x, via.y, 3, 0, 2 * Math.PI);
                ctx.fill();

                // Draw drill hole
                ctx.fillStyle = 'var(--pcb-green)';
                ctx.beginPath();
                ctx.arc(via.x, via.y, 1, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'var(--via-color)';
            });
        }

        function toggleLayer(layerName) {
            drawPCBCanvas();
        }

        function updateGrid() {
            drawPCBCanvas();
        }

        function autoRoute() {
            // Simple auto-routing algorithm
            AppState.pcbWorkbench.ratsnest.forEach(net => {
                if (!net.routed) {
                    const fromComp = AppState.pcbWorkbench.components.find(c => net.from.startsWith(c.id));
                    const toComp = AppState.pcbWorkbench.components.find(c => net.to.startsWith(c.id));

                    if (fromComp && toComp && fromComp.placed && toComp.placed) {
                        // Create simple straight trace
                        AppState.pcbWorkbench.traces.push({
                            id: `trace_${Date.now()}`,
                            layer: 'top',
                            points: [
                                { x: fromComp.x, y: fromComp.y },
                                { x: toComp.x, y: toComp.y }
                            ],
                            net: net.from + '-' + net.to
                        });

                        net.routed = true;
                    }
                }
            });

            drawPCBCanvas();
            updatePCBInfo();
        }

        function clearPCB() {
            AppState.pcbWorkbench.traces = [];
            AppState.pcbWorkbench.vias = [];
            AppState.pcbWorkbench.components.forEach(comp => {
                comp.placed = false;
                comp.x = 0;
                comp.y = 0;
            });
            AppState.pcbWorkbench.ratsnest.forEach(net => {
                net.routed = false;
            });

            populateComponentBin();
            drawPCBCanvas();
            updatePCBInfo();
            update3DPreview();
        }

        function updatePCBInfo() {
            const unroutedNets = AppState.pcbWorkbench.ratsnest.filter(net => !net.routed).length;
            const placedComponents = AppState.pcbWorkbench.components.filter(comp => comp.placed).length;
            const totalComponents = AppState.pcbWorkbench.components.length;
            const completion = totalComponents > 0 ? Math.round((placedComponents / totalComponents) * 100) : 0;

            document.getElementById('netsRemaining').textContent = unroutedNets;
            document.getElementById('pcbComponentCount').textContent = `${placedComponents}/${totalComponents}`;
            document.getElementById('traceCount').textContent = AppState.pcbWorkbench.traces.length;
            document.getElementById('viaCount').textContent = AppState.pcbWorkbench.vias.length;
            document.getElementById('completionPercent').textContent = completion + '%';

            // Update DRC status
            updateDRCStatus();
        }

        function updateDRCStatus() {
            const drcStatus = document.getElementById('drcStatus');
            const unroutedNets = AppState.pcbWorkbench.ratsnest.filter(net => !net.routed).length;

            if (unroutedNets === 0 && AppState.pcbWorkbench.components.every(comp => comp.placed)) {
                drcStatus.className = 'drc-status drc-pass';
                drcStatus.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 0.5rem;">✅ DRC: PASS</div>
                    <div style="font-size: 0.85rem;">All design rules satisfied</div>
                `;
                AppState.pcbWorkbench.drcStatus = 'pass';
            } else {
                drcStatus.className = 'drc-status drc-fail';
                drcStatus.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 0.5rem;">❌ DRC: FAIL</div>
                    <div style="font-size: 0.85rem;">Unrouted nets: ${unroutedNets}</div>
                `;
                AppState.pcbWorkbench.drcStatus = 'fail';
            }
        }

        function update3DPreview() {
            const canvas = document.getElementById('preview3DCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Simple 3D-like rendering
            ctx.fillStyle = '#2a2a2a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw PCB board in 3D perspective
            const boardWidth = canvas.width * 0.8;
            const boardHeight = canvas.height * 0.6;
            const offsetX = (canvas.width - boardWidth) / 2;
            const offsetY = (canvas.height - boardHeight) / 2;

            // PCB substrate
            ctx.fillStyle = 'var(--pcb-green)';
            ctx.fillRect(offsetX, offsetY, boardWidth, boardHeight);

            // Draw components in 3D
            AppState.pcbWorkbench.components.forEach(comp => {
                if (comp.placed) {
                    const x = offsetX + (comp.x / 800) * boardWidth;
                    const y = offsetY + (comp.y / 600) * boardHeight;

                    // Component shadow
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                    ctx.fillRect(x + 2, y + 2, 12, 8);

                    // Component body
                    const footprint = ComponentFootprints[comp.footprint];
                    ctx.fillStyle = footprint ? footprint.color : '#333';
                    ctx.fillRect(x, y, 10, 6);
                }
            });
        }

        // Export Functions
        function exportGerber() {
            const fileList = [
                'top_copper.gbr',
                'bottom_copper.gbr',
                'top_silkscreen.gto',
                'bottom_silkscreen.gbo',
                'drill_holes.drl',
                'board_outline.gko'
            ];

            document.getElementById('exportFileList').innerHTML = fileList.map(file =>
                `<div>📄 ${file}</div>`
            ).join('');

            document.getElementById('exportModal').classList.remove('hidden');
        }

        function exportBOM() {
            const bomData = AppState.pcbWorkbench.components.map(comp => ({
                designator: comp.id,
                footprint: comp.footprint,
                value: comp.properties.resistance || comp.properties.capacitance || comp.properties.gain || 'N/A'
            }));

            let csvContent = 'Designator,Footprint,Value\n';
            bomData.forEach(item => {
                csvContent += `${item.designator},${item.footprint},${item.value}\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'bill_of_materials.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function export3D() {
            alert('3D model export functionality would integrate with professional CAD software in a production environment.');
        }

        function closeExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        // Utility Functions
        function updateProceedToPCBButton() {
            const btn = document.getElementById('proceedToPCBBtn');
            if (AppState.circuitSimulation.readyForPCB && AppState.circuitSimulation.components.length > 0) {
                btn.disabled = false;
                btn.textContent = '🔧 Proceed to PCB Layout';
            } else {
                btn.disabled = true;
                btn.textContent = '🔧 Design Circuit First';
            }
        }

        function updateCircuitInfo() {
            document.getElementById('componentCount').textContent = AppState.circuitSimulation.components.length;
            document.getElementById('connectionCount').textContent = AppState.circuitSimulation.connections.length;
            updateProceedToPCBButton();
        }

        function showHelp() {
            document.getElementById('helpModal').classList.remove('hidden');
        }

        function hideHelp() {
            document.getElementById('helpModal').classList.add('hidden');
        }

        // Event Handlers
        function handleCanvasClick(event) {
            // Circuit canvas click handler
            console.log('Circuit canvas clicked');
        }

        function handleCanvasMouseDown(event) {
            // Circuit canvas mouse down handler
        }

        function handleCanvasMouseMove(event) {
            // Circuit canvas mouse move handler
        }

        function handleCanvasMouseUp(event) {
            // Circuit canvas mouse up handler
        }

        function handlePCBCanvasMouseDown(event) {
            // PCB canvas mouse down handler
        }

        function handlePCBCanvasMouseMove(event) {
            // PCB canvas mouse move handler
        }

        function handlePCBCanvasMouseUp(event) {
            // PCB canvas mouse up handler
        }

        function drawCircuitCanvas() {
            // Circuit canvas drawing function
            const canvas = document.getElementById('circuitCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function startOscilloscopeAnimation() {
            if (AppState.animationFrameId) {
                cancelAnimationFrame(AppState.animationFrameId);
            }

            if (AppState.powerOn && AppState.selectedModule) {
                drawEnhancedOscilloscope();
                startMiniOscilloscope();
                updatePreviewValues();
            }
        }

        function drawEnhancedOscilloscope() {
            const canvas = document.getElementById('oscilloscope');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw enhanced grid
            drawEnhancedGrid(ctx, canvas.width, canvas.height);

            if (AppState.selectedModule) {
                const module = LabModules[AppState.selectedModule];
                const time = Date.now() * 0.001;

                if (AppState.analysisMode) {
                    drawAnalysisWaveforms(ctx, canvas.width, canvas.height, time);
                } else {
                    drawRealisticPhysiologicalSignal(ctx, canvas.width, canvas.height, time, module);
                }

                // Draw measurement cursors and readouts
                drawMeasurementOverlay(ctx, canvas.width, canvas.height);
            }

            AppState.animationFrameId = requestAnimationFrame(drawEnhancedOscilloscope);
        }

        function drawEnhancedGrid(ctx, width, height) {
            // Major grid lines
            ctx.strokeStyle = '#1a2332';
            ctx.lineWidth = 1;

            const majorSpacing = 40;
            for (let x = 0; x <= width; x += majorSpacing) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y <= height; y += 30) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Minor grid lines
            ctx.strokeStyle = '#0f1419';
            ctx.lineWidth = 0.5;

            const minorSpacing = 8;
            for (let x = 0; x <= width; x += minorSpacing) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y <= height; y += 6) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Center lines
            ctx.strokeStyle = '#2a3441';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, height / 2);
            ctx.lineTo(width, height / 2);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(width / 2, 0);
            ctx.lineTo(width / 2, height);
            ctx.stroke();
        }

        function drawRealisticPhysiologicalSignal(ctx, width, height, time, module) {
            const centerY = height / 2;
            const scale = 80;

            // Main signal
            ctx.strokeStyle = '#00ff88';
            ctx.lineWidth = 3;
            ctx.shadowColor = '#00ff88';
            ctx.shadowBlur = 5;
            ctx.beginPath();

            const points = [];
            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                let y = centerY;

                switch (module.name.split(' ')[0]) {
                    case 'ECG':
                        y = centerY - scale * generateRealisticECG(t);
                        break;
                    case 'EMG':
                        y = centerY - scale * generateRealisticEMG(t);
                        break;
                    case 'EEG':
                        y = centerY - scale * generateRealisticEEG(t);
                        break;
                    case 'EOG':
                        y = centerY - scale * generateRealisticEOG(t);
                        break;
                    default:
                        y = centerY - scale * Math.sin(2 * Math.PI * module.frequency * t * 0.1) * module.amplitude;
                }

                points.push({x, y});

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
            ctx.shadowBlur = 0;

            // Add noise overlay for realism
            ctx.strokeStyle = 'rgba(0, 255, 136, 0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();

            for (let x = 0; x < width; x += 2) {
                const noise = (Math.random() - 0.5) * 10;
                const baseY = points[x] ? points[x].y : centerY;
                ctx.moveTo(x, baseY + noise);
                ctx.lineTo(x + 1, baseY + noise);
            }
            ctx.stroke();
        }

        function clearOscilloscope() {
            if (AppState.animationFrameId) {
                cancelAnimationFrame(AppState.animationFrameId);
                AppState.animationFrameId = null;
            }

            const canvas = document.getElementById('oscilloscope');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }

            const miniCanvas = document.getElementById('miniOscilloscope');
            if (miniCanvas) {
                const ctx = miniCanvas.getContext('2d');
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, miniCanvas.width, miniCanvas.height);
            }
        }

        function exportData(format = 'csv') {
            if (!AppState.selectedModule) {
                alert('Please select a measurement module first.');
                return;
            }

            const module = LabModules[AppState.selectedModule];
            const sampleRate = 1000; // Hz
            const duration = 10; // seconds
            const samples = sampleRate * duration;

            let data = [];
            let content = '';

            // Generate sample data
            for (let i = 0; i < samples; i++) {
                const time = i / sampleRate;
                let voltage = 0;

                switch (module.name.split(' ')[0]) {
                    case 'ECG':
                        voltage = generateRealisticECG(time);
                        break;
                    case 'EMG':
                        voltage = generateRealisticEMG(time);
                        break;
                    case 'EEG':
                        voltage = generateRealisticEEG(time);
                        break;
                    case 'EOG':
                        voltage = generateRealisticEOG(time);
                        break;
                    default:
                        voltage = Math.sin(2 * Math.PI * module.frequency * time) * module.amplitude;
                }

                data.push({time, voltage});
            }

            // Format data based on export type
            switch (format) {
                case 'csv':
                    content = 'Time (s),Voltage (V)\n';
                    data.forEach(point => {
                        content += `${point.time.toFixed(4)},${point.voltage.toFixed(6)}\n`;
                    });
                    downloadFile(`${AppState.selectedModule}_data.csv`, content, 'text/csv');
                    break;

                case 'matlab':
                    content = `% ${module.name} Data Export\n`;
                    content += `% Generated by Bio-Signal Explorer v4.0\n\n`;
                    content += `time = [${data.map(p => p.time.toFixed(4)).join(', ')}];\n`;
                    content += `voltage = [${data.map(p => p.voltage.toFixed(6)).join(', ')}];\n\n`;
                    content += `% Plot the data\nplot(time, voltage);\n`;
                    content += `xlabel('Time (s)');\nylabel('Voltage (V)');\n`;
                    content += `title('${module.name} Signal');\ngrid on;\n`;
                    downloadFile(`${AppState.selectedModule}_data.m`, content, 'text/plain');
                    break;

                case 'python':
                    content = `# ${module.name} Data Export\n`;
                    content += `# Generated by Bio-Signal Explorer v4.0\n\n`;
                    content += `import numpy as np\nimport matplotlib.pyplot as plt\n\n`;
                    content += `time = np.array([${data.slice(0, 100).map(p => p.time.toFixed(4)).join(', ')}])\n`;
                    content += `voltage = np.array([${data.slice(0, 100).map(p => p.voltage.toFixed(6)).join(', ')}])\n\n`;
                    content += `# Plot the data\nplt.figure(figsize=(10, 6))\n`;
                    content += `plt.plot(time, voltage)\nplt.xlabel('Time (s)')\n`;
                    content += `plt.ylabel('Voltage (V)')\nplt.title('${module.name} Signal')\n`;
                    content += `plt.grid(True)\nplt.show()\n`;
                    downloadFile(`${AppState.selectedModule}_analysis.py`, content, 'text/plain');
                    break;
            }
        }

        // Realistic Physiological Signal Generators
        function generateRealisticECG(t) {
            const heartRate = 72; // BPM
            const period = 60 / heartRate;
            const phase = (t % period) / period;

            let signal = 0;

            // P wave (0.08-0.12s)
            if (phase >= 0.0 && phase <= 0.1) {
                const p = (phase - 0.05) / 0.05;
                signal += 0.15 * Math.exp(-p * p * 8);
            }

            // QRS complex (0.06-0.10s)
            if (phase >= 0.15 && phase <= 0.25) {
                const qrs = (phase - 0.2) / 0.05;
                if (phase < 0.18) {
                    signal -= 0.1 * Math.exp(-((phase - 0.16) / 0.01) ** 2);
                } else if (phase < 0.22) {
                    signal += 1.0 * Math.exp(-((phase - 0.2) / 0.01) ** 2);
                } else {
                    signal -= 0.2 * Math.exp(-((phase - 0.23) / 0.01) ** 2);
                }
            }

            // T wave (0.16s)
            if (phase >= 0.35 && phase <= 0.55) {
                const t_wave = (phase - 0.45) / 0.1;
                signal += 0.3 * Math.exp(-t_wave * t_wave * 4);
            }

            // Add baseline noise
            signal += (Math.random() - 0.5) * 0.02;

            return signal;
        }

        function generateRealisticEMG(t) {
            // Simulate muscle activation bursts
            const burstFreq = 2; // Hz
            const burstPhase = (t * burstFreq) % 1;

            let signal = 0;

            if (burstPhase < 0.3) {
                // Active muscle contraction
                const envelope = Math.sin(burstPhase * Math.PI / 0.3);
                const highFreq = Math.sin(2 * Math.PI * 150 * t) * envelope;
                const medFreq = Math.sin(2 * Math.PI * 80 * t) * envelope * 0.7;
                signal = (highFreq + medFreq) * 0.5;
            }

            // Add EMG noise
            signal += (Math.random() - 0.5) * 0.1;

            return signal;
        }

        function generateRealisticEEG(t) {
            // Alpha waves (8-13 Hz)
            const alpha = Math.sin(2 * Math.PI * 10 * t) * 0.3;

            // Beta waves (13-30 Hz)
            const beta = Math.sin(2 * Math.PI * 20 * t) * 0.15;

            // Theta waves (4-8 Hz)
            const theta = Math.sin(2 * Math.PI * 6 * t) * 0.2;

            // Delta waves (0.5-4 Hz)
            const delta = Math.sin(2 * Math.PI * 2 * t) * 0.1;

            // Combine waves with random amplitude modulation
            const modulation = 0.8 + 0.2 * Math.sin(2 * Math.PI * 0.1 * t);
            let signal = (alpha + beta + theta + delta) * modulation;

            // Add EEG noise
            signal += (Math.random() - 0.5) * 0.05;

            return signal;
        }

        function generateRealisticEOG(t) {
            // Simulate eye movements (saccades)
            const saccadeFreq = 0.5; // Hz
            const saccadePhase = (t * saccadeFreq) % 1;

            let signal = 0;

            if (saccadePhase < 0.1) {
                // Rapid eye movement
                const saccade = Math.sin(saccadePhase * Math.PI / 0.1);
                signal = saccade * 0.8;
            } else if (saccadePhase > 0.8) {
                // Return movement
                const returnPhase = (saccadePhase - 0.8) / 0.2;
                signal = -Math.sin(returnPhase * Math.PI) * 0.4;
            }

            // Add slow drift
            signal += 0.1 * Math.sin(2 * Math.PI * 0.05 * t);

            // Add noise
            signal += (Math.random() - 0.5) * 0.03;

            return signal;
        }

        // Additional Interactive Functions
        function startMiniOscilloscope() {
            const canvas = document.getElementById('miniOscilloscope');
            if (!canvas) return;

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const ctx = canvas.getContext('2d');

            function drawMini() {
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                if (AppState.selectedModule) {
                    const module = LabModules[AppState.selectedModule];
                    const time = Date.now() * 0.001;

                    ctx.strokeStyle = '#00ff88';
                    ctx.lineWidth = 2;
                    ctx.beginPath();

                    for (let x = 0; x < canvas.width; x++) {
                        const t = (x / canvas.width) * 2 + time;
                        let y = canvas.height / 2;

                        switch (module.name.split(' ')[0]) {
                            case 'ECG':
                                y -= 30 * generateRealisticECG(t);
                                break;
                            case 'EMG':
                                y -= 30 * generateRealisticEMG(t);
                                break;
                            case 'EEG':
                                y -= 30 * generateRealisticEEG(t);
                                break;
                            case 'EOG':
                                y -= 30 * generateRealisticEOG(t);
                                break;
                        }

                        if (x === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }

                    ctx.stroke();
                }

                requestAnimationFrame(drawMini);
            }

            drawMini();
        }

        function updatePreviewValues() {
            if (!AppState.selectedModule) return;

            const module = LabModules[AppState.selectedModule];
            const freqSlider = document.getElementById('freqSlider');
            const ampSlider = document.getElementById('ampSlider');

            if (freqSlider && ampSlider) {
                document.getElementById('previewFreq').textContent = freqSlider.value + 'Hz';
                document.getElementById('previewAmp').textContent = parseFloat(ampSlider.value).toFixed(1) + 'V';

                // Calculate heart rate for ECG
                if (AppState.selectedModule === 'ecg') {
                    const heartRate = Math.round(72 + (Math.random() - 0.5) * 10);
                    document.getElementById('previewHR').textContent = heartRate + ' BPM';
                } else {
                    document.getElementById('previewHR').textContent = '--';
                }
            }
        }

        function drawMeasurementOverlay(ctx, width, height) {
            // This would draw measurement cursors and readouts
            // Implementation would depend on user interaction
        }

        function drawAnalysisWaveforms(ctx, width, height, time) {
            // Draw multiple analysis waveforms when in analysis mode
            const centerY = height / 2;

            // Input signal
            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY - 40 + 30 * Math.sin(2 * Math.PI * 1 * t);
                if (x === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();

            // Output signal
            ctx.strokeStyle = '#ff6b35';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY + 40 + 30 * Math.sin(2 * Math.PI * 1 * t) * 0.7;
                if (x === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();
        }

        function downloadFile(filename, content, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Additional Interactive Functions
        function startProcedureWaveform() {
            const canvas = document.getElementById('procedureWaveform');
            if (!canvas) return;

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const ctx = canvas.getContext('2d');

            function drawProcedure() {
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw grid
                ctx.strokeStyle = '#1a2332';
                ctx.lineWidth = 1;
                for (let x = 0; x <= canvas.width; x += 20) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                for (let y = 0; y <= canvas.height; y += 15) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }

                if (AppState.selectedModule) {
                    const module = LabModules[AppState.selectedModule];
                    const time = Date.now() * 0.001;

                    ctx.strokeStyle = '#00ff88';
                    ctx.lineWidth = 2;
                    ctx.beginPath();

                    for (let x = 0; x < canvas.width; x++) {
                        const t = (x / canvas.width) * 3 + time;
                        let y = canvas.height / 2;

                        switch (module.name.split(' ')[0]) {
                            case 'ECG':
                                y -= 25 * generateRealisticECG(t);
                                break;
                            case 'EMG':
                                y -= 25 * generateRealisticEMG(t);
                                break;
                            case 'EEG':
                                y -= 25 * generateRealisticEEG(t);
                                break;
                            case 'EOG':
                                y -= 25 * generateRealisticEOG(t);
                                break;
                        }

                        if (x === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }

                    ctx.stroke();
                }

                requestAnimationFrame(drawProcedure);
            }

            drawProcedure();
        }

        function startExportPreview() {
            const canvas = document.getElementById('exportPreview');
            if (!canvas) return;

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const ctx = canvas.getContext('2d');

            function drawExport() {
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw professional grid
                ctx.strokeStyle = '#2a3441';
                ctx.lineWidth = 1;
                for (let x = 0; x <= canvas.width; x += 40) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                for (let y = 0; y <= canvas.height; y += 20) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }

                if (AppState.selectedModule) {
                    const module = LabModules[AppState.selectedModule];
                    const time = Date.now() * 0.001;

                    // Draw main signal
                    ctx.strokeStyle = '#00d4ff';
                    ctx.lineWidth = 3;
                    ctx.shadowColor = '#00d4ff';
                    ctx.shadowBlur = 3;
                    ctx.beginPath();

                    for (let x = 0; x < canvas.width; x++) {
                        const t = (x / canvas.width) * 5 + time;
                        let y = canvas.height / 2;

                        switch (module.name.split(' ')[0]) {
                            case 'ECG':
                                y -= 30 * generateRealisticECG(t);
                                break;
                            case 'EMG':
                                y -= 30 * generateRealisticEMG(t);
                                break;
                            case 'EEG':
                                y -= 30 * generateRealisticEEG(t);
                                break;
                            case 'EOG':
                                y -= 30 * generateRealisticEOG(t);
                                break;
                        }

                        if (x === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }

                    ctx.stroke();
                    ctx.shadowBlur = 0;

                    // Add measurement markers
                    ctx.fillStyle = '#ff6b35';
                    for (let x = 0; x < canvas.width; x += 100) {
                        ctx.beginPath();
                        ctx.arc(x, canvas.height / 2, 3, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }

                requestAnimationFrame(drawExport);
            }

            drawExport();
        }

        // Enhanced Oscilloscope Controls
        function toggleOscilloscopeGrid() {
            // Toggle grid visibility
            console.log('Grid toggled');
        }

        function captureWaveform() {
            // Capture current waveform
            if (!AppState.selectedModule) {
                alert('Please select a measurement module first.');
                return;
            }

            const canvas = document.getElementById('oscilloscope');
            if (canvas) {
                const dataURL = canvas.toDataURL('image/png');
                const a = document.createElement('a');
                a.href = dataURL;
                a.download = `${AppState.selectedModule}_waveform_${Date.now()}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        }

        function resetOscilloscope() {
            // Reset oscilloscope settings
            clearOscilloscope();
            if (AppState.powerOn && AppState.selectedModule) {
                setTimeout(() => {
                    startOscilloscopeAnimation();
                }, 100);
            }
        }

        // Drag and Drop for PCB Components
        document.addEventListener('dragover', function(event) {
            event.preventDefault();
        });

        document.addEventListener('drop', function(event) {
            event.preventDefault();

            if (event.target.id === 'pcbCanvas') {
                const componentId = event.dataTransfer.getData('text/plain');
                const component = AppState.pcbWorkbench.components.find(c => c.id === componentId);

                if (component && !component.placed) {
                    const rect = event.target.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    const gridSize = parseFloat(document.getElementById('gridSize').value) * 96;
                    component.x = Math.round(x / gridSize) * gridSize;
                    component.y = Math.round(y / gridSize) * gridSize;
                    component.placed = true;

                    populateComponentBin();
                    drawPCBCanvas();
                    updatePCBInfo();
                    update3DPreview();
                }
            }

            // Remove dragging class
            document.querySelectorAll('.dragging').forEach(el => {
                el.classList.remove('dragging');
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }

            // Close modals when clicking outside
            if (event.target.id === 'helpModal') {
                hideHelp();
            }
            if (event.target.id === 'exportModal') {
                closeExportModal();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize language system first
            initializeLanguage();

            // Initialize simulation lab
            updateSensorGrid();
            updateTabContent();

            // Ensure all elements are properly translated
            setTimeout(() => {
                updateAllTranslations();
            }, 100);

            // Set up canvas elements
            const oscilloscope = document.getElementById('oscilloscope');
            if (oscilloscope) {
                oscilloscope.width = oscilloscope.offsetWidth;
                oscilloscope.height = oscilloscope.offsetHeight;
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (AppState.currentMode === 'design') {
                    initializeCircuitCanvas();
                } else if (AppState.currentMode === 'pcb') {
                    initializePCBCanvas();
                    update3DPreview();
                } else {
                    const oscilloscope = document.getElementById('oscilloscope');
                    if (oscilloscope) {
                        oscilloscope.width = oscilloscope.offsetWidth;
                        oscilloscope.height = oscilloscope.offsetHeight;
                    }
                }
            });

            // Initialize with simulation mode
            switchMode('simulation');

            // Initialize PCB tool selection
            selectPCBTool('select');

            console.log(`🌐 Bio-Signal Explorer v4.0 initialized with language: ${AppState.currentLanguage === 'en' ? 'English' : 'العربية'}`);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case '1':
                        event.preventDefault();
                        switchMode('simulation');
                        break;
                    case '2':
                        event.preventDefault();
                        switchMode('design');
                        break;
                    case '3':
                        event.preventDefault();
                        switchMode('pcb');
                        break;
                    case 's':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            if (AppState.circuitSimulation.running) {
                                // stopCircuitSimulation();
                            } else {
                                // runCircuitSimulation();
                            }
                        }
                        break;
                    case 'c':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            // clearCanvas();
                        } else if (AppState.currentMode === 'pcb') {
                            clearPCB();
                        }
                        break;
                }
            }

            // Escape key to close modals and dropdowns
            if (event.key === 'Escape') {
                hideHelp();
                closeExportModal();
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }

            // Delete key to remove selected PCB components
            if (event.key === 'Delete' && AppState.currentMode === 'pcb') {
                // Remove selected component logic would go here
            }
        });

        // Additional helper functions for circuit simulation
        function runCircuitSimulation() {
            AppState.circuitSimulation.running = true;
            document.getElementById('runSimBtn').disabled = true;
            document.getElementById('stopSimBtn').disabled = false;
            document.getElementById('simStatus').textContent = 'Running';
            AppState.circuitSimulation.readyForPCB = true;
            updateProceedToPCBButton();
        }

        function stopCircuitSimulation() {
            AppState.circuitSimulation.running = false;
            document.getElementById('runSimBtn').disabled = false;
            document.getElementById('stopSimBtn').disabled = true;
            document.getElementById('simStatus').textContent = 'Stopped';
        }

        function generateBodePlot() {
            // Bode plot generation logic
            console.log('Generating Bode plot...');
        }

        function clearCanvas() {
            AppState.circuitSimulation.components = [];
            AppState.circuitSimulation.connections = [];
            AppState.circuitSimulation.readyForPCB = false;
            drawCircuitCanvas();
            updateCircuitInfo();
        }

        function showAnalysisTab(tabName) {
            // Update analysis tab buttons
            document.querySelectorAll('.analysis-tab').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';
        }

        function applyProperties() {
            // Apply component properties
        }

        function closeProperties() {
            // Close properties panel
        }

        // LCD Animation Functions
        let lcdAnimationId = null;

        function startLCDAnimation() {
            if (lcdAnimationId) {
                clearInterval(lcdAnimationId);
            }

            lcdAnimationId = setInterval(() => {
                if (AppState.powerOn) {
                    updateLCDStatus();
                }
            }, 2000);
        }

        function stopLCDAnimation() {
            if (lcdAnimationId) {
                clearInterval(lcdAnimationId);
                lcdAnimationId = null;
            }
        }

        function updateLCDStatus() {
            const lcdText = document.getElementById('virtualLCD')?.querySelector('.lcd-text');
            if (!lcdText) return;

            const currentTime = new Date().toLocaleTimeString();
            const currentLang = AppState.currentLanguage;

            if (AppState.selectedModule) {
                const module = LabModules[AppState.selectedModule];
                const moduleName = typeof module.name === 'object' ? module.name[currentLang] : module.name;
                const connectedSensors = Array.from(document.querySelectorAll('#sensorGrid input:checked')).length;
                const requiredSensors = module.requiredSensors.length;

                let status = currentLang === 'en' ? 'READY' : 'جاهز';
                if (connectedSensors === requiredSensors) {
                    status = currentLang === 'en' ? 'MEASURING' : 'يقيس';
                } else if (connectedSensors > 0) {
                    status = currentLang === 'en' ? 'PARTIAL' : 'جزئي';
                }

                const moduleText = currentLang === 'en' ? 'MODULE' : 'الوحدة';
                const sensorsText = currentLang === 'en' ? 'SENSORS' : 'أجهزة الاستشعار';
                const connectedText = currentLang === 'en' ? 'CONNECTED' : 'متصل';
                const statusText = currentLang === 'en' ? 'STATUS' : 'الحالة';
                const timeText = currentLang === 'en' ? 'TIME' : 'الوقت';

                lcdText.innerHTML = `> ${moduleText}: ${moduleName}<br>> ${sensorsText}: ${connectedSensors}/${requiredSensors} ${connectedText}<br>> ${statusText}: ${status} | ${timeText}: ${currentTime}`;
            } else {
                const systemOnlineText = currentLang === 'en' ? 'SYSTEM ONLINE' : 'النظام يعمل';
                const selectModuleText = currentLang === 'en' ? 'SELECT MODULE TO BEGIN' : 'اختر الوحدة للبدء';
                const timeText = currentLang === 'en' ? 'TIME' : 'الوقت';

                lcdText.innerHTML = `> ${systemOnlineText}<br>> ${selectModuleText}<br>> ${timeText}: ${currentTime}`;
            }
        }

        // Debug function for language toggle
        function testLanguageToggle() {
            console.log('🧪 Testing language toggle functionality...');
            console.log('Current language:', AppState.currentLanguage);
            console.log('Available translations:', Object.keys(Translations));
            console.log('Language button element:', document.getElementById('langToggle'));

            // Test toggle
            toggleLanguage();
            console.log('After toggle - Current language:', AppState.currentLanguage);
        }

        // Make test function globally available
        window.testLanguageToggle = testLanguageToggle;

        // Enhanced Console welcome message
        console.log(`
        🎉 Bio-Signal Explorer v4.0 Enhanced Successfully Loaded!

        ✨ NEW DYNAMIC FEATURES:
        • 🌊 Realistic physiological waveform generation
        • 📊 Enhanced oscilloscope with professional grid
        • 🎮 Interactive controls and animations
        • 📱 Dynamic LCD display with real-time updates
        • 🎨 Professional circuit diagrams and block diagrams
        • 📸 Waveform capture and export capabilities
        • 🔄 Smooth animations and transitions
        • 💫 Glowing effects and visual feedback
        • 🌐 BILINGUAL SUPPORT: English ⇄ Arabic

        🔬 REALISTIC SIGNAL GENERATION:
        • ECG: P-QRS-T complex with realistic timing
        • EMG: Muscle activation bursts with noise
        • EEG: Alpha, beta, theta, delta wave combinations
        • EOG: Saccadic eye movements with drift

        🎯 INTERACTIVE ELEMENTS:
        • Real-time parameter updates
        • Dynamic status indicators
        • Animated icons and effects
        • Professional measurement overlays

        🌐 LANGUAGE FEATURES:
        • Complete English/Arabic interface
        • RTL (Right-to-Left) support for Arabic
        • Bilingual module descriptions and procedures
        • Localized status messages and controls
        • Persistent language preference storage

        Features:
        • 🔬 Simulation Lab - Biomedical measurement simulation
        • ⚡ Circuit Design Toolkit - Interactive circuit design
        • 🔧 PCB Workbench - Complete PCB layout and design

        Keyboard Shortcuts:
        • Ctrl+1: Simulation Lab
        • Ctrl+2: Circuit Design
        • Ctrl+3: PCB Workbench
        • Ctrl+S: Start/Stop simulation
        • Escape: Close modals

        Ready for the most immersive biomedical engineering education experience!
        Now accessible to both English and Arabic speaking students worldwide!

        🧪 TESTING LANGUAGE TOGGLE:
        • Click the 🌐 language button in the header
        • Or run: testLanguageToggle() in console
        • Watch the interface switch between English and Arabic
        • Notice RTL layout changes for Arabic

        Developed by Dr. Mohammed Yagoub Esmail
        Sudan University of Science and Technology - BME Department
        `);
    </script>
</body>
</html>
