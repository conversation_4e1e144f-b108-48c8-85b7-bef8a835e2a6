<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v4.0: Complete Biomedical Device Prototyping Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-dark: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --panel-bg: #252b3d;
            --border-color: #3a4556;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --text-muted: #64748b;
            --accent-cyan: #00d4ff;
            --accent-green: #00ff88;
            --accent-yellow: #ffeb3b;
            --accent-red: #ff4757;
            --accent-purple: #8b5cf6;
            --accent-orange: #ff6b35;
            --button-bg: #2563eb;
            --button-hover: #1d4ed8;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --grid-color: #2a3441;
            --pcb-green: #1a5d1a;
            --copper-color: #cd7f32;
            --via-color: #c0c0c0;
            --trace-color: #ffd700;
            --ratsnest-color: #ff69b4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(90deg, var(--panel-bg) 0%, var(--bg-secondary) 100%);
            padding: 1rem 2rem;
            border-bottom: 2px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1800px;
            margin: 0 auto;
        }

        .header-left h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-left p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .header-right {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Mode Switcher */
        .mode-switcher {
            background: var(--bg-secondary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .mode-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .mode-btn {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--panel-bg);
            color: var(--text-primary);
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
        }

        .mode-btn:hover {
            border-color: var(--accent-cyan);
            background: var(--bg-secondary);
            transform: translateY(-2px);
        }

        .mode-btn.active {
            border-color: var(--accent-cyan);
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            color: var(--bg-dark);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .mode-icon {
            font-size: 1.2rem;
        }

        .mode-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-red);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 10px;
            font-weight: 700;
        }

        /* Main Container */
        .main-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Common Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--button-bg), var(--accent-cyan));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: var(--bg-dark);
        }

        .btn-danger {
            background: var(--error);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            min-width: 200px;
            max-height: 300px;
            overflow-y: auto;
        }

        .dropdown.active .dropdown-content {
            display: block;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: var(--bg-secondary);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .component-category {
            font-weight: 600;
            color: var(--accent-cyan);
            background: var(--bg-secondary);
        }

        .component-item {
            padding-left: 1.5rem;
            font-size: 0.85rem;
        }

        /* View Containers */
        .view-container {
            display: none;
            height: calc(100vh - 200px);
        }

        .view-container.active {
            display: flex;
        }

        /* Simulation Lab Styles */
        .simulation-lab {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }

        .control-panel, .display-area {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .display-area {
            display: flex;
            flex-direction: column;
        }

        /* Circuit Design Toolkit Styles */
        .circuit-toolkit {
            flex-direction: column;
            gap: 1rem;
        }

        .toolkit-toolbar {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .toolkit-main {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 1rem;
            flex: 1;
            min-height: 0;
        }

        .canvas-container {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        .circuit-canvas {
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(var(--grid-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
            background-size: 20px 20px;
            border-radius: 8px;
            cursor: crosshair;
        }

        .analysis-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* PCB Workbench Styles */
        .pcb-workbench {
            flex-direction: row;
            gap: 1rem;
        }

        .pcb-left-panel {
            width: 280px;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-center {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-right-panel {
            width: 320px;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pcb-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .pcb-canvas-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .pcb-canvas {
            width: 100%;
            height: 100%;
            background: var(--pcb-green);
            border-radius: 8px;
            cursor: crosshair;
        }

        .component-bin {
            max-height: 300px;
            overflow-y: auto;
        }

        .component-bin-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: grab;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .component-bin-item:hover {
            background: var(--border-color);
            transform: translateY(-2px);
        }

        .component-bin-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .layer-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-secondary);
            border-radius: 6px;
        }

        .layer-checkbox {
            width: 1rem;
            height: 1rem;
        }

        .drc-status {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .drc-pass {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid var(--success);
            color: var(--success);
        }

        .drc-fail {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid var(--error);
            color: var(--error);
        }

        .preview-3d {
            height: 200px;
            background: #1a1a1a;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .preview-3d canvas {
            width: 100%;
            height: 100%;
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .simulation-lab {
                grid-template-columns: 1fr;
            }
            
            .toolkit-main {
                grid-template-columns: 1fr;
            }
            
            .pcb-workbench {
                flex-direction: column;
            }
            
            .pcb-left-panel,
            .pcb-right-panel {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .mode-buttons {
                flex-direction: column;
            }
            
            .main-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <h1>Bio-Signal Explorer v4.0</h1>
                <p>Complete Biomedical Device Prototyping Platform</p>
            </div>
            <div class="header-right">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">
                    🏠 Home
                </button>
                <button type="button" class="btn btn-secondary" onclick="showHelp()">
                    ❓ Help
                </button>
            </div>
        </div>
    </header>

    <!-- Mode Switcher -->
    <div class="mode-switcher">
        <div class="mode-buttons">
            <button type="button" class="mode-btn active" id="simModeBtn" onclick="switchMode('simulation')">
                <span class="mode-icon">🔬</span>
                Simulation Lab
                <span class="mode-badge">1</span>
            </button>
            <button type="button" class="mode-btn" id="designModeBtn" onclick="switchMode('design')">
                <span class="mode-icon">⚡</span>
                Circuit Design Toolkit
                <span class="mode-badge">2</span>
            </button>
            <button type="button" class="mode-btn" id="pcbModeBtn" onclick="switchMode('pcb')">
                <span class="mode-icon">🔧</span>
                PCB Workbench
                <span class="mode-badge">3</span>
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Simulation Lab View -->
        <div class="view-container active" id="simulationView">
            <div class="simulation-lab">
                <!-- Control Panel -->
                <div class="control-panel">
                    <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">🎛️ Control Hub</h2>

                    <!-- Power Control -->
                    <div style="margin-bottom: 1.5rem;">
                        <button type="button" class="btn btn-success" id="powerBtn" onclick="togglePower()" style="width: 100%;">
                            ⚡ Power On
                        </button>
                    </div>

                    <!-- Module Selection -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Measurement Module:</label>
                        <div style="display: flex; gap: 0.5rem;">
                            <select id="moduleSelect" style="flex: 1; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); color: var(--text-primary);" onchange="selectModule()">
                                <option value="">Select Module...</option>
                                <option value="ecg">ECG - Electrocardiogram</option>
                                <option value="emg">EMG - Electromyography</option>
                                <option value="eeg">EEG - Electroencephalography</option>
                                <option value="eog">EOG - Electrooculography</option>
                                <option value="bp">Blood Pressure</option>
                                <option value="impedance">Body Impedance</option>
                            </select>
                            <button type="button" class="btn btn-secondary" id="viewCircuitBtn" onclick="viewInternalCircuit()" title="View Internal Circuit" disabled>
                                🔍
                            </button>
                        </div>
                    </div>

                    <!-- Sensor Connections -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Sensor Connections:</label>
                        <div id="sensorGrid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem;">
                            <!-- Sensors will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Function Generator -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Function Generator:</label>
                        <div style="margin-bottom: 0.75rem;">
                            <label style="font-size: 0.85rem; color: var(--text-secondary);">Frequency: <span id="freqValue">100</span> Hz</label>
                            <input type="range" id="freqSlider" min="1" max="1000" value="100" style="width: 100%; margin-top: 0.25rem;" oninput="updateFrequency()">
                        </div>
                        <div>
                            <label style="font-size: 0.85rem; color: var(--text-secondary);">Amplitude: <span id="ampValue">1.0</span> Vpp</label>
                            <input type="range" id="ampSlider" min="0.1" max="10" step="0.1" value="1.0" style="width: 100%; margin-top: 0.25rem;" oninput="updateAmplitude()">
                        </div>
                    </div>

                    <!-- Analysis Mode -->
                    <div>
                        <button type="button" class="btn btn-warning" id="analysisBtn" onclick="toggleAnalysisMode()" style="width: 100%;" disabled>
                            📊 Enter Circuit Analysis
                        </button>
                    </div>
                </div>

                <!-- Display Area -->
                <div class="display-area">
                    <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">📺 System Display</h2>

                    <!-- Status Panel -->
                    <div id="statusPanel" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; text-align: center; color: var(--warning);">
                        System is powered off. Click Power On to begin.
                    </div>

                    <!-- Virtual LCD -->
                    <div id="virtualLCD" style="background: #000; color: var(--accent-green); font-family: 'Roboto Mono', monospace; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; min-height: 80px; border: 2px solid var(--border-color);">
                        > SYSTEM OFFLINE<br>
                        > POWER ON TO START
                    </div>

                    <!-- Oscilloscope -->
                    <div style="flex: 1; background: #000; border: 2px solid var(--border-color); border-radius: 8px; position: relative; margin-bottom: 1rem;">
                        <canvas id="oscilloscope" style="width: 100%; height: 100%; border-radius: 6px;"></canvas>
                    </div>

                    <!-- Tabs -->
                    <div style="display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem;">
                        <button type="button" class="tab-btn active" onclick="showTab('info')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                            📋 Module Info
                        </button>
                        <button type="button" class="tab-btn" onclick="showTab('procedure')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                            📝 Procedure
                        </button>
                        <button type="button" class="tab-btn" onclick="showTab('export')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                            💾 Export Data
                        </button>
                    </div>

                    <!-- Tab Content -->
                    <div id="tabContent" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; min-height: 120px;">
                        Select a measurement module to view information.
                    </div>
                </div>
            </div>
        </div>

        <!-- Circuit Design Toolkit View -->
        <div class="view-container" id="designView">
            <div class="circuit-toolkit">
                <!-- Toolbar -->
                <div class="toolkit-toolbar">
                    <!-- Component Library -->
                    <div class="dropdown" id="componentDropdown">
                        <button type="button" class="dropdown-btn" onclick="toggleDropdown('componentDropdown')">
                            🧩 Add Component
                            <span style="margin-left: auto;">▼</span>
                        </button>
                        <div class="dropdown-content">
                            <div class="dropdown-item component-category">Sources</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('function-generator')">📡 Function Generator</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('bio-signal')">💓 Bio-Signal Source</div>

                            <div class="dropdown-item component-category">Passive Components</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('resistor')">🔧 Resistor</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('capacitor')">⚡ Capacitor</div>

                            <div class="dropdown-item component-category">Active Components</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('op-amp')">🔺 Op-Amp</div>

                            <div class="dropdown-item component-category">Measurement</div>
                            <div class="dropdown-item component-item" onclick="selectComponent('oscilloscope')">📊 Oscilloscope Probe</div>
                        </div>
                    </div>

                    <!-- Template Library -->
                    <div class="dropdown" id="templateDropdown">
                        <button type="button" class="dropdown-btn" onclick="toggleDropdown('templateDropdown')">
                            📋 Load Template
                            <span style="margin-left: auto;">▼</span>
                        </button>
                        <div class="dropdown-content">
                            <div class="dropdown-item" onclick="loadTemplate('rc-lowpass')">RC Low-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('rc-highpass')">RC High-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('non-inverting-amp')">Non-inverting Op-Amp</div>
                            <div class="dropdown-item" onclick="loadTemplate('bandpass-filter')">Band-Pass Filter</div>
                            <div class="dropdown-item" onclick="loadTemplate('ecg-frontend')">ECG Frontend Circuit</div>
                            <div class="dropdown-item" onclick="loadTemplate('emg-amplifier')">EMG Amplifier</div>
                        </div>
                    </div>

                    <!-- Simulation Controls -->
                    <div style="display: flex; gap: 0.5rem;">
                        <button type="button" class="btn btn-success" id="runSimBtn" onclick="runCircuitSimulation()">
                            ▶️ Run Simulation
                        </button>
                        <button type="button" class="btn btn-danger" id="stopSimBtn" onclick="stopCircuitSimulation()" disabled>
                            ⏹️ Stop
                        </button>
                    </div>

                    <!-- Analysis Tools -->
                    <div style="display: flex; gap: 0.5rem;">
                        <button type="button" class="btn btn-primary" onclick="generateBodePlot()">
                            📈 Bode Plot
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearCanvas()">
                            🗑️ Clear
                        </button>
                    </div>

                    <!-- PCB Transition -->
                    <div style="margin-left: auto;">
                        <button type="button" class="btn btn-warning" id="proceedToPCBBtn" onclick="proceedToPCB()" disabled>
                            🔧 Proceed to PCB Layout
                        </button>
                    </div>
                </div>

                <!-- Main Toolkit Area -->
                <div class="toolkit-main">
                    <!-- Canvas Container -->
                    <div class="canvas-container">
                        <canvas id="circuitCanvas" class="circuit-canvas"></canvas>

                        <!-- Component Properties Panel -->
                        <div id="propertiesPanel" class="hidden" style="position: absolute; top: 20px; right: 20px; background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 8px; padding: 1rem; min-width: 200px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
                            <h4 style="margin-bottom: 0.5rem; color: var(--accent-cyan);">Component Properties</h4>
                            <div id="propertiesContent">
                                <!-- Properties will be populated by JavaScript -->
                            </div>
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                                <button type="button" class="btn btn-primary" onclick="applyProperties()" style="flex: 1; font-size: 0.8rem;">Apply</button>
                                <button type="button" class="btn btn-secondary" onclick="closeProperties()" style="flex: 1; font-size: 0.8rem;">Close</button>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Panel -->
                    <div class="analysis-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📊 Analysis</h3>

                        <!-- Analysis Tabs -->
                        <div style="display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem;">
                            <button type="button" class="analysis-tab active" onclick="showAnalysisTab('oscilloscope')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                                📺 Scope
                            </button>
                            <button type="button" class="analysis-tab" onclick="showAnalysisTab('bode')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                                📈 Bode
                            </button>
                        </div>

                        <!-- Analysis Content -->
                        <div id="analysisContent" style="flex: 1; background: #000; border: 1px solid var(--border-color); border-radius: 8px; position: relative; min-height: 200px;">
                            <canvas id="analysisCanvas" style="width: 100%; height: 100%; border-radius: 7px;"></canvas>
                            <div id="analysisPlaceholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-muted); text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                                <div>Run simulation to see results</div>
                            </div>
                        </div>

                        <!-- Analysis Info -->
                        <div id="analysisInfo" style="margin-top: 1rem; padding: 0.75rem; background: var(--bg-secondary); border-radius: 8px; font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Status:</strong> <span id="simStatus">Ready</span></div>
                            <div><strong>Components:</strong> <span id="componentCount">0</span></div>
                            <div><strong>Connections:</strong> <span id="connectionCount">0</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PCB Workbench View -->
        <div class="view-container" id="pcbView">
            <div class="pcb-workbench">
                <!-- Left Panel -->
                <div class="pcb-left-panel">
                    <!-- Component Bin -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📦 Component Bin</h3>
                        <div class="component-bin" id="componentBin">
                            <!-- Components will be populated from circuit design -->
                        </div>
                    </div>

                    <!-- Layers Panel -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📋 Layers</h3>
                        <div id="layersPanel">
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="topCopperLayer" checked onchange="toggleLayer('topCopper')">
                                <label for="topCopperLayer">Top Copper</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="bottomCopperLayer" checked onchange="toggleLayer('bottomCopper')">
                                <label for="bottomCopperLayer">Bottom Copper</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="silkscreenLayer" checked onchange="toggleLayer('silkscreen')">
                                <label for="silkscreenLayer">Silkscreen</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="drillLayer" checked onchange="toggleLayer('drill')">
                                <label for="drillLayer">Drill Holes</label>
                            </div>
                            <div class="layer-control">
                                <input type="checkbox" class="layer-checkbox" id="ratsnestLayer" checked onchange="toggleLayer('ratsnest')">
                                <label for="ratsnestLayer">Ratsnest</label>
                            </div>
                        </div>
                    </div>

                    <!-- PCB Tools -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">🛠️ Tools</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <button type="button" class="btn btn-secondary" id="selectTool" onclick="selectPCBTool('select')" style="width: 100%;">
                                🖱️ Select
                            </button>
                            <button type="button" class="btn btn-secondary" id="routeTool" onclick="selectPCBTool('route')" style="width: 100%;">
                                🔗 Route Trace
                            </button>
                            <button type="button" class="btn btn-secondary" id="viaTool" onclick="selectPCBTool('via')" style="width: 100%;">
                                ⚫ Place Via
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearPCB()" style="width: 100%;">
                                🗑️ Clear PCB
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Center Panel -->
                <div class="pcb-center">
                    <!-- PCB Toolbar -->
                    <div class="pcb-panel" style="padding: 0.75rem;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <label style="font-size: 0.9rem;">Layer:</label>
                                <select id="currentLayer" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary);">
                                    <option value="top">Top Copper</option>
                                    <option value="bottom">Bottom Copper</option>
                                </select>
                            </div>
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <label style="font-size: 0.9rem;">Grid:</label>
                                <select id="gridSize" onchange="updateGrid()" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary);">
                                    <option value="0.1">0.1 inch</option>
                                    <option value="0.05">0.05 inch</option>
                                    <option value="0.025">0.025 inch</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="autoRoute()" style="margin-left: auto;">
                                🤖 Auto Route
                            </button>
                        </div>
                    </div>

                    <!-- PCB Canvas -->
                    <div class="pcb-panel pcb-canvas-container">
                        <canvas id="pcbCanvas" class="pcb-canvas"></canvas>
                    </div>
                </div>

                <!-- Right Panel -->
                <div class="pcb-right-panel">
                    <!-- Design Rule Checker -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">✅ Design Rules</h3>
                        <div id="drcStatus" class="drc-status drc-pass">
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">✅ DRC: PASS</div>
                            <div style="font-size: 0.85rem;">All design rules satisfied</div>
                        </div>
                        <div style="font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Nets Remaining:</strong> <span id="netsRemaining">0</span></div>
                            <div><strong>Components:</strong> <span id="pcbComponentCount">0</span></div>
                            <div><strong>Traces:</strong> <span id="traceCount">0</span></div>
                            <div><strong>Vias:</strong> <span id="viaCount">0</span></div>
                        </div>
                    </div>

                    <!-- 3D Preview -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">🎯 3D Preview</h3>
                        <div class="preview-3d" id="preview3D">
                            <canvas id="preview3DCanvas" style="width: 100%; height: 100%;"></canvas>
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-muted); text-align: center; font-size: 0.85rem;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔧</div>
                                <div>Place components to see 3D view</div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Panel -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📤 Export</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <button type="button" class="btn btn-success" onclick="exportGerber()" style="width: 100%;">
                                📄 Export Gerber & Drill Files
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="exportBOM()" style="width: 100%;">
                                📋 Export Bill of Materials
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="export3D()" style="width: 100%;">
                                🎯 Export 3D Model
                            </button>
                        </div>
                    </div>

                    <!-- PCB Statistics -->
                    <div class="pcb-panel">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📊 Statistics</h3>
                        <div style="font-size: 0.85rem; color: var(--text-secondary);">
                            <div><strong>Board Size:</strong> <span id="boardSize">50mm x 50mm</span></div>
                            <div><strong>Total Trace Length:</strong> <span id="totalTraceLength">0 mm</span></div>
                            <div><strong>Layer Count:</strong> <span id="layerCount">2</span></div>
                            <div><strong>Completion:</strong> <span id="completionPercent">0%</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
        <div style="background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 16px; padding: 2rem; max-width: 700px; max-height: 80vh; overflow-y: auto; margin: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h2 style="color: var(--accent-cyan);">🎓 Bio-Signal Explorer v4.0 Guide</h2>
                <button type="button" onclick="hideHelp()" style="background: none; border: none; color: var(--text-secondary); font-size: 1.5rem; cursor: pointer;">×</button>
            </div>

            <div style="space-y: 1rem;">
                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔬 1. Simulation Lab</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Start here to understand biomedical measurement concepts. Power on the system, select a module, connect sensors, and observe real-time physiological signals.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">⚡ 2. Circuit Design Toolkit</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Design and analyze circuits from scratch. Add components, connect them, run simulations, and generate Bode plots. Use the "View Internal Circuit" button to explore module designs.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔧 3. PCB Workbench</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">NEW! Transform your circuit into a physical PCB layout. Place components, route traces, check design rules, and view real-time 3D preview. Export Gerber files for manufacturing.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔄 Workflow Integration</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Follow the complete prototyping lifecycle: Simulation → Circuit Design → PCB Layout. Use "Proceed to PCB Layout" to automatically transfer your circuit design.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">⌨️ Keyboard Shortcuts</h3>
                    <div style="color: var(--text-secondary); line-height: 1.6;">
                        <div>• <strong>Ctrl+1:</strong> Switch to Simulation Lab</div>
                        <div>• <strong>Ctrl+2:</strong> Switch to Circuit Design</div>
                        <div>• <strong>Ctrl+3:</strong> Switch to PCB Workbench</div>
                        <div>• <strong>Ctrl+S:</strong> Start/Stop simulation</div>
                        <div>• <strong>Escape:</strong> Close modals and dropdowns</div>
                    </div>
                </div>

                <div>
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🎯 PCB Tools</h3>
                    <div style="color: var(--text-secondary); line-height: 1.6;">
                        <div>• <strong>Select Tool:</strong> Move and rotate components</div>
                        <div>• <strong>Route Tool:</strong> Draw copper traces between pads</div>
                        <div>• <strong>Via Tool:</strong> Connect between layers</div>
                        <div>• <strong>Auto Route:</strong> Automatic trace routing</div>
                        <div>• <strong>DRC:</strong> Real-time design rule checking</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Success Modal -->
    <div id="exportModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
        <div style="background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 16px; padding: 2rem; max-width: 500px; margin: 2rem; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
            <h2 style="color: var(--accent-green); margin-bottom: 1rem;">Export Successful!</h2>
            <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">The following manufacturing files have been generated:</p>
            <div id="exportFileList" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; text-align: left; font-family: 'Roboto Mono', monospace; font-size: 0.85rem;">
                <!-- File list will be populated by JavaScript -->
            </div>
            <button type="button" class="btn btn-primary" onclick="closeExportModal()">Close</button>
        </div>
    </div>

    <script>
        // Global State Management
        const AppState = {
            currentMode: 'simulation',
            powerOn: false,
            selectedModule: null,
            analysisMode: false,
            circuitSimulation: {
                running: false,
                components: [],
                connections: [],
                selectedComponent: null,
                draggedComponent: null,
                readyForPCB: false
            },
            pcbWorkbench: {
                components: [],
                traces: [],
                vias: [],
                ratsnest: [],
                selectedTool: 'select',
                currentLayer: 'top',
                gridSize: 0.1,
                placedComponents: [],
                drcStatus: 'pass'
            },
            animationFrameId: null
        };

        // Module Definitions
        const LabModules = {
            ecg: {
                name: "ECG - Electrocardiogram",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN5'],
                description: "Measures electrical activity of the heart through electrodes placed on the skin.",
                procedure: "1. Clean skin areas\n2. Attach electrodes to RA, LA, LL, RL\n3. Ask subject to relax\n4. Record P-QRS-T waveform",
                circuitTemplate: 'ecg-frontend',
                frequency: 1.2,
                amplitude: 1.0
            },
            emg: {
                name: "EMG - Electromyography",
                requiredSensors: ['IN1', 'IN2', 'IN5'],
                description: "Records electrical potential generated by muscle cells during activation.",
                procedure: "1. Place electrodes over target muscle\n2. Place ground electrode on bony prominence\n3. Record during muscle contractions",
                circuitTemplate: 'emg-amplifier',
                frequency: 150,
                amplitude: 0.5
            },
            eeg: {
                name: "EEG - Electroencephalography",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: "Measures electrical activity of the brain from the scalp.",
                procedure: "1. Place electrodes according to 10-20 system\n2. Ensure impedance < 5kΩ\n3. Record with eyes open/closed",
                circuitTemplate: 'bandpass-filter',
                frequency: 10,
                amplitude: 0.1
            },
            eog: {
                name: "EOG - Electrooculography",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4', 'IN5'],
                description: "Measures eye movements based on corneal-retinal potential difference.",
                procedure: "1. Place electrodes around eyes\n2. Place ground on forehead\n3. Ask subject to follow target",
                circuitTemplate: 'rc-highpass',
                frequency: 0.5,
                amplitude: 0.8
            },
            bp: {
                name: "Blood Pressure Monitor",
                requiredSensors: ['IN6'],
                description: "Oscillometric blood pressure measurement using pressure cuff.",
                procedure: "1. Wrap cuff around upper arm\n2. Inflate above systolic pressure\n3. Slowly deflate and record oscillations",
                circuitTemplate: 'rc-lowpass',
                frequency: 1.0,
                amplitude: 2.0
            },
            impedance: {
                name: "Body Impedance Analyzer",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: "Measures body composition using bioelectrical impedance analysis.",
                procedure: "1. Connect four electrodes\n2. Apply safe AC current\n3. Measure voltage drop\n4. Calculate impedance",
                circuitTemplate: 'non-inverting-amp',
                frequency: 1000,
                amplitude: 0.001
            }
        };

        // Circuit Templates with PCB Component Mappings
        const CircuitTemplates = {
            'rc-lowpass': {
                name: 'RC Low-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 1000, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'resistor', x: 250, y: 150, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'capacitor', x: 400, y: 150, id: 'c1', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'scope1-in' }
                ]
            },
            'rc-highpass': {
                name: 'RC High-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 100, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'capacitor', x: 250, y: 150, id: 'c1', properties: { capacitance: 1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 400, y: 150, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'non-inverting-amp': {
                name: 'Non-inverting Op-Amp Amplifier',
                components: [
                    { type: 'function-generator', x: 100, y: 200, id: 'fg1', properties: { frequency: 1000, amplitude: 0.1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'op-amp', x: 300, y: 200, id: 'op1', properties: { gain: 10 }, footprint: 'SOIC8' },
                    { type: 'resistor', x: 250, y: 250, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'resistor', x: 350, y: 250, id: 'r2', properties: { resistance: 9000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'op1-in+' },
                    { from: 'op1-in-', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'op1-out' },
                    { from: 'op1-out', to: 'scope1-in' }
                ]
            },
            'bandpass-filter': {
                name: 'Band-Pass Filter (HPF + LPF)',
                components: [
                    { type: 'function-generator', x: 80, y: 200, id: 'fg1', properties: { frequency: 100, amplitude: 1 }, footprint: 'SMA_CONNECTOR' },
                    { type: 'capacitor', x: 200, y: 200, id: 'c1', properties: { capacitance: 1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 320, y: 200, id: 'r1', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'resistor', x: 440, y: 200, id: 'r2', properties: { resistance: 1000 }, footprint: 'R_0805' },
                    { type: 'capacitor', x: 560, y: 200, id: 'c2', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 680, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            },
            'ecg-frontend': {
                name: 'ECG Frontend Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'ecg1', properties: { signalType: 'ECG', amplitude: 0.001 }, footprint: 'CONN_3PIN' },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 1000 }, footprint: 'SOIC8' },
                    { type: 'capacitor', x: 360, y: 200, id: 'c1', properties: { capacitance: 0.1 }, footprint: 'C_0805' },
                    { type: 'resistor', x: 480, y: 200, id: 'r1', properties: { resistance: 10000 }, footprint: 'R_0805' },
                    { type: 'oscilloscope', x: 620, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'ecg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'emg-amplifier': {
                name: 'EMG Amplifier Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'emg1', properties: { signalType: 'EMG', amplitude: 0.0001 }, footprint: 'CONN_3PIN' },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 10000 }, footprint: 'SOIC8' },
                    { type: 'capacitor', x: 360, y: 180, id: 'c1', properties: { capacitance: 0.01 }, footprint: 'C_0805' },
                    { type: 'capacitor', x: 360, y: 220, id: 'c2', properties: { capacitance: 10 }, footprint: 'C_0805' },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {}, footprint: 'SMA_CONNECTOR' }
                ],
                connections: [
                    { from: 'emg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            }
        };

        // PCB Component Footprints
        const ComponentFootprints = {
            'R_0805': { width: 2.0, height: 1.25, pads: [{ x: -0.95, y: 0 }, { x: 0.95, y: 0 }], color: '#2c2c2c' },
            'C_0805': { width: 2.0, height: 1.25, pads: [{ x: -0.95, y: 0 }, { x: 0.95, y: 0 }], color: '#8b4513' },
            'SOIC8': { width: 5.0, height: 4.0, pads: [
                { x: -1.905, y: -1.27 }, { x: -1.905, y: -0.635 }, { x: -1.905, y: 0.635 }, { x: -1.905, y: 1.27 },
                { x: 1.905, y: 1.27 }, { x: 1.905, y: 0.635 }, { x: 1.905, y: -0.635 }, { x: 1.905, y: -1.27 }
            ], color: '#1a1a1a' },
            'SMA_CONNECTOR': { width: 6.0, height: 6.0, pads: [{ x: 0, y: 0 }], color: '#c0c0c0' },
            'CONN_3PIN': { width: 7.5, height: 2.5, pads: [{ x: -2.54, y: 0 }, { x: 0, y: 0 }, { x: 2.54, y: 0 }], color: '#333333' }
        };

        // Mode Management Functions
        function switchMode(mode) {
            AppState.currentMode = mode;

            // Update button states
            document.getElementById('simModeBtn').classList.toggle('active', mode === 'simulation');
            document.getElementById('designModeBtn').classList.toggle('active', mode === 'design');
            document.getElementById('pcbModeBtn').classList.toggle('active', mode === 'pcb');

            // Show/hide views
            document.getElementById('simulationView').classList.toggle('active', mode === 'simulation');
            document.getElementById('designView').classList.toggle('active', mode === 'design');
            document.getElementById('pcbView').classList.toggle('active', mode === 'pcb');

            // Initialize canvas based on mode
            if (mode === 'design') {
                initializeCircuitCanvas();
            } else if (mode === 'pcb') {
                initializePCBCanvas();
                update3DPreview();
            }
        }

        // Simulation Lab Functions
        function togglePower() {
            AppState.powerOn = !AppState.powerOn;
            const powerBtn = document.getElementById('powerBtn');
            const statusPanel = document.getElementById('statusPanel');
            const virtualLCD = document.getElementById('virtualLCD');

            if (AppState.powerOn) {
                powerBtn.textContent = '🔴 Power Off';
                powerBtn.className = 'btn btn-danger';
                statusPanel.textContent = 'System powered on. Select a measurement module.';
                statusPanel.style.color = 'var(--success)';
                virtualLCD.innerHTML = '> SYSTEM ONLINE<br>> SELECT MODULE TO BEGIN';

                // Enable controls
                document.getElementById('moduleSelect').disabled = false;
                document.getElementById('analysisBtn').disabled = false;
            } else {
                powerBtn.textContent = '⚡ Power On';
                powerBtn.className = 'btn btn-success';
                statusPanel.textContent = 'System is powered off. Click Power On to begin.';
                statusPanel.style.color = 'var(--warning)';
                virtualLCD.innerHTML = '> SYSTEM OFFLINE<br>> POWER ON TO START';

                // Disable controls
                document.getElementById('moduleSelect').disabled = true;
                document.getElementById('moduleSelect').value = '';
                document.getElementById('analysisBtn').disabled = true;
                document.getElementById('viewCircuitBtn').disabled = true;
                AppState.selectedModule = null;

                // Clear oscilloscope
                clearOscilloscope();
            }

            updateSensorGrid();
            updateTabContent();
        }

        function selectModule() {
            const moduleSelect = document.getElementById('moduleSelect');
            const selectedValue = moduleSelect.value;

            if (selectedValue && LabModules[selectedValue]) {
                AppState.selectedModule = selectedValue;
                document.getElementById('viewCircuitBtn').disabled = false;

                const module = LabModules[selectedValue];
                const virtualLCD = document.getElementById('virtualLCD');
                virtualLCD.innerHTML = `> MODULE: ${module.name}<br>> CONNECT SENSORS TO BEGIN`;

                updateSensorGrid();
                updateTabContent();
                startOscilloscopeAnimation();
            } else {
                AppState.selectedModule = null;
                document.getElementById('viewCircuitBtn').disabled = true;
                clearOscilloscope();
            }
        }

        function updateSensorGrid() {
            const sensorGrid = document.getElementById('sensorGrid');
            const sensors = ['IN1', 'IN2', 'IN3', 'IN4', 'IN5', 'IN6', 'IN7'];

            sensorGrid.innerHTML = '';

            sensors.forEach(sensor => {
                const sensorDiv = document.createElement('div');
                sensorDiv.style.cssText = 'display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: var(--bg-secondary); border-radius: 6px; border: 1px solid var(--border-color);';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = sensor;
                checkbox.disabled = !AppState.powerOn;

                const label = document.createElement('label');
                label.textContent = sensor;
                label.style.cssText = 'flex: 1; font-size: 0.85rem; cursor: pointer;';
                label.setAttribute('for', sensor);

                const led = document.createElement('div');
                led.style.cssText = 'width: 8px; height: 8px; border-radius: 50%; background: var(--border-color); transition: all 0.3s ease;';

                // Check if sensor is required for current module
                if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                    const requiredSensors = LabModules[AppState.selectedModule].requiredSensors;
                    if (requiredSensors.includes(sensor)) {
                        checkbox.addEventListener('change', function() {
                            if (this.checked) {
                                led.style.background = 'var(--accent-green)';
                                led.style.boxShadow = '0 0 8px var(--accent-green)';
                            } else {
                                led.style.background = 'var(--border-color)';
                                led.style.boxShadow = 'none';
                            }
                        });
                    }
                }

                sensorDiv.appendChild(checkbox);
                sensorDiv.appendChild(label);
                sensorDiv.appendChild(led);
                sensorGrid.appendChild(sensorDiv);
            });
        }

        function updateFrequency() {
            const freqSlider = document.getElementById('freqSlider');
            const freqValue = document.getElementById('freqValue');
            freqValue.textContent = freqSlider.value;
        }

        function updateAmplitude() {
            const ampSlider = document.getElementById('ampSlider');
            const ampValue = document.getElementById('ampValue');
            ampValue.textContent = parseFloat(ampSlider.value).toFixed(1);
        }

        function toggleAnalysisMode() {
            AppState.analysisMode = !AppState.analysisMode;
            const analysisBtn = document.getElementById('analysisBtn');

            if (AppState.analysisMode) {
                analysisBtn.textContent = '📊 Exit Analysis Mode';
                analysisBtn.className = 'btn btn-secondary';
            } else {
                analysisBtn.textContent = '📊 Enter Circuit Analysis';
                analysisBtn.className = 'btn btn-warning';
            }
        }

        function viewInternalCircuit() {
            if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                const module = LabModules[AppState.selectedModule];
                switchMode('design');
                loadTemplate(module.circuitTemplate);
            }
        }

        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';

            updateTabContent(tabName);
        }

        function updateTabContent(activeTab = 'info') {
            const tabContent = document.getElementById('tabContent');

            if (!AppState.selectedModule) {
                tabContent.innerHTML = 'Select a measurement module to view information.';
                return;
            }

            const module = LabModules[AppState.selectedModule];

            switch (activeTab) {
                case 'info':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">${module.name}</h4>
                        <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 1rem;">${module.description}</p>
                        <div style="background: var(--panel-bg); padding: 0.75rem; border-radius: 6px; border-left: 3px solid var(--accent-green);">
                            <strong>Required Sensors:</strong> ${module.requiredSensors.join(', ')}
                        </div>
                    `;
                    break;
                case 'procedure':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">Measurement Procedure</h4>
                        <pre style="color: var(--text-secondary); line-height: 1.6; white-space: pre-wrap; font-family: inherit;">${module.procedure}</pre>
                    `;
                    break;
                case 'export':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">Data Export</h4>
                        <p style="color: var(--text-secondary); margin-bottom: 1rem;">Export measurement data for offline analysis:</p>
                        <button type="button" class="btn btn-primary" onclick="exportData()">📥 Download Sample Data (.csv)</button>
                    `;
                    break;
            }
        }

        // Circuit Design Toolkit Functions
        function initializeCircuitCanvas() {
            const canvas = document.getElementById('circuitCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add event listeners
            canvas.addEventListener('click', handleCanvasClick);
            canvas.addEventListener('mousedown', handleCanvasMouseDown);
            canvas.addEventListener('mousemove', handleCanvasMouseMove);
            canvas.addEventListener('mouseup', handleCanvasMouseUp);

            updateCircuitInfo();
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const allDropdowns = document.querySelectorAll('.dropdown');

            // Close other dropdowns
            allDropdowns.forEach(d => {
                if (d.id !== dropdownId) {
                    d.classList.remove('active');
                }
            });

            dropdown.classList.toggle('active');
        }

        function selectComponent(componentType) {
            AppState.circuitSimulation.selectedComponent = componentType;
            document.getElementById('componentDropdown').classList.remove('active');

            // Change cursor to indicate component selection
            const canvas = document.getElementById('circuitCanvas');
            if (canvas) canvas.style.cursor = 'crosshair';
        }

        function loadTemplate(templateName) {
            if (CircuitTemplates[templateName]) {
                const template = CircuitTemplates[templateName];
                AppState.circuitSimulation.components = [...template.components];
                AppState.circuitSimulation.connections = [...template.connections];
                AppState.circuitSimulation.readyForPCB = true;

                drawCircuitCanvas();
                updateCircuitInfo();
                updateProceedToPCBButton();

                document.getElementById('templateDropdown').classList.remove('active');
            }
        }

        function proceedToPCB() {
            if (AppState.circuitSimulation.readyForPCB && AppState.circuitSimulation.components.length > 0) {
                // Transfer circuit data to PCB workbench
                transferCircuitToPCB();
                switchMode('pcb');
            }
        }

        function transferCircuitToPCB() {
            // Clear existing PCB data
            AppState.pcbWorkbench.components = [];
            AppState.pcbWorkbench.ratsnest = [];

            // Convert circuit components to PCB components
            AppState.circuitSimulation.components.forEach(comp => {
                if (comp.footprint) {
                    AppState.pcbWorkbench.components.push({
                        id: comp.id,
                        type: comp.type,
                        footprint: comp.footprint,
                        x: 0, // Will be placed by user
                        y: 0,
                        rotation: 0,
                        placed: false,
                        properties: comp.properties
                    });
                }
            });

            // Generate ratsnest from connections
            AppState.circuitSimulation.connections.forEach(conn => {
                AppState.pcbWorkbench.ratsnest.push({
                    from: conn.from,
                    to: conn.to,
                    routed: false
                });
            });

            // Populate component bin
            populateComponentBin();
            updatePCBInfo();
        }

        // PCB Workbench Functions
        function initializePCBCanvas() {
            const canvas = document.getElementById('pcbCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Add event listeners
            canvas.addEventListener('click', handlePCBCanvasClick);
            canvas.addEventListener('mousedown', handlePCBCanvasMouseDown);
            canvas.addEventListener('mousemove', handlePCBCanvasMouseMove);
            canvas.addEventListener('mouseup', handlePCBCanvasMouseUp);

            drawPCBCanvas();
        }

        function populateComponentBin() {
            const componentBin = document.getElementById('componentBin');
            componentBin.innerHTML = '';

            AppState.pcbWorkbench.components.forEach(comp => {
                if (!comp.placed) {
                    const item = document.createElement('div');
                    item.className = 'component-bin-item';
                    item.draggable = true;
                    item.dataset.componentId = comp.id;

                    const icon = getComponentIcon(comp.type);
                    const footprint = ComponentFootprints[comp.footprint];

                    item.innerHTML = `
                        <span style="font-size: 1.2rem;">${icon}</span>
                        <div>
                            <div style="font-weight: 600; font-size: 0.9rem;">${comp.id}</div>
                            <div style="font-size: 0.75rem; color: var(--text-muted);">${comp.footprint}</div>
                        </div>
                    `;

                    item.addEventListener('dragstart', handleComponentDragStart);
                    componentBin.appendChild(item);
                }
            });
        }

        function getComponentIcon(type) {
            const icons = {
                'resistor': '🔧',
                'capacitor': '⚡',
                'op-amp': '🔺',
                'function-generator': '📡',
                'bio-signal': '💓',
                'oscilloscope': '📊'
            };
            return icons[type] || '🔲';
        }

        function handleComponentDragStart(event) {
            event.dataTransfer.setData('text/plain', event.target.dataset.componentId);
            event.target.classList.add('dragging');
        }

        function handlePCBCanvasClick(event) {
            const canvas = event.target;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            const gridSize = parseFloat(document.getElementById('gridSize').value) * 96; // Convert inches to pixels (96 DPI)
            const gridX = Math.round(x / gridSize) * gridSize;
            const gridY = Math.round(y / gridSize) * gridSize;

            switch (AppState.pcbWorkbench.selectedTool) {
                case 'select':
                    handleSelectTool(gridX, gridY);
                    break;
                case 'route':
                    handleRouteTool(gridX, gridY);
                    break;
                case 'via':
                    handleViaTool(gridX, gridY);
                    break;
            }
        }

        function handleSelectTool(x, y) {
            // Find component at position
            const component = findPCBComponentAt(x, y);
            if (component) {
                // Select component for editing
                showPCBComponentProperties(component);
            }
        }

        function handleRouteTool(x, y) {
            // Implement trace routing logic
            console.log('Route tool clicked at:', x, y);
        }

        function handleViaTool(x, y) {
            // Place via
            AppState.pcbWorkbench.vias.push({
                x: x,
                y: y,
                id: `via_${Date.now()}`
            });

            drawPCBCanvas();
            updatePCBInfo();
        }

        function findPCBComponentAt(x, y) {
            return AppState.pcbWorkbench.components.find(comp => {
                if (!comp.placed) return false;

                const footprint = ComponentFootprints[comp.footprint];
                const halfWidth = (footprint.width * 9.6) / 2; // Convert mm to pixels
                const halfHeight = (footprint.height * 9.6) / 2;

                return x >= comp.x - halfWidth && x <= comp.x + halfWidth &&
                       y >= comp.y - halfHeight && y <= comp.y + halfHeight;
            });
        }

        function selectPCBTool(tool) {
            AppState.pcbWorkbench.selectedTool = tool;

            // Update tool button states
            document.querySelectorAll('#selectTool, #routeTool, #viaTool').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
            });

            const toolBtn = document.getElementById(tool + 'Tool');
            if (toolBtn) {
                toolBtn.classList.remove('btn-secondary');
                toolBtn.classList.add('btn-primary');
            }
        }

        function drawPCBCanvas() {
            const canvas = document.getElementById('pcbCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Clear canvas with PCB green
            ctx.fillStyle = 'var(--pcb-green)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw grid
            drawPCBGrid(ctx, canvas.width, canvas.height);

            // Draw board outline
            drawBoardOutline(ctx, canvas.width, canvas.height);

            // Draw ratsnest (if layer is visible)
            if (document.getElementById('ratsnestLayer').checked) {
                drawRatsnest(ctx);
            }

            // Draw placed components
            drawPCBComponents(ctx);

            // Draw traces
            if (document.getElementById('topCopperLayer').checked) {
                drawTraces(ctx, 'top');
            }
            if (document.getElementById('bottomCopperLayer').checked) {
                drawTraces(ctx, 'bottom');
            }

            // Draw vias
            if (document.getElementById('drillLayer').checked) {
                drawVias(ctx);
            }
        }

        function drawPCBGrid(ctx, width, height) {
            const gridSize = parseFloat(document.getElementById('gridSize').value) * 96; // Convert inches to pixels

            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Vertical lines
            for (let x = 0; x <= width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Horizontal lines
            for (let y = 0; y <= height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        }

        function drawBoardOutline(ctx, width, height) {
            const margin = 50;
            ctx.strokeStyle = 'var(--accent-yellow)';
            ctx.lineWidth = 2;
            ctx.strokeRect(margin, margin, width - 2 * margin, height - 2 * margin);
        }

        function drawRatsnest(ctx) {
            ctx.strokeStyle = 'var(--ratsnest-color)';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);

            AppState.pcbWorkbench.ratsnest.forEach(net => {
                if (!net.routed) {
                    const fromComp = AppState.pcbWorkbench.components.find(c => net.from.startsWith(c.id));
                    const toComp = AppState.pcbWorkbench.components.find(c => net.to.startsWith(c.id));

                    if (fromComp && toComp && fromComp.placed && toComp.placed) {
                        ctx.beginPath();
                        ctx.moveTo(fromComp.x, fromComp.y);
                        ctx.lineTo(toComp.x, toComp.y);
                        ctx.stroke();
                    }
                }
            });

            ctx.setLineDash([]);
        }

        function drawPCBComponents(ctx) {
            AppState.pcbWorkbench.components.forEach(comp => {
                if (comp.placed) {
                    drawPCBComponent(ctx, comp);
                }
            });
        }

        function drawPCBComponent(ctx, comp) {
            const footprint = ComponentFootprints[comp.footprint];
            if (!footprint) return;

            ctx.save();
            ctx.translate(comp.x, comp.y);
            ctx.rotate(comp.rotation * Math.PI / 180);

            // Draw component body
            const width = footprint.width * 9.6; // Convert mm to pixels
            const height = footprint.height * 9.6;

            ctx.fillStyle = footprint.color;
            ctx.fillRect(-width/2, -height/2, width, height);

            // Draw pads
            ctx.fillStyle = 'var(--copper-color)';
            footprint.pads.forEach(pad => {
                const padSize = 1.5 * 9.6; // 1.5mm pad size
                ctx.fillRect(pad.x * 9.6 - padSize/2, pad.y * 9.6 - padSize/2, padSize, padSize);
            });

            // Draw component label
            if (document.getElementById('silkscreenLayer').checked) {
                ctx.fillStyle = 'white';
                ctx.font = '10px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(comp.id, 0, height/2 + 15);
            }

            ctx.restore();
        }

        function drawTraces(ctx, layer) {
            ctx.strokeStyle = layer === 'top' ? 'var(--trace-color)' : 'var(--copper-color)';
            ctx.lineWidth = 3;

            AppState.pcbWorkbench.traces.forEach(trace => {
                if (trace.layer === layer) {
                    ctx.beginPath();
                    ctx.moveTo(trace.points[0].x, trace.points[0].y);
                    for (let i = 1; i < trace.points.length; i++) {
                        ctx.lineTo(trace.points[i].x, trace.points[i].y);
                    }
                    ctx.stroke();
                }
            });
        }

        function drawVias(ctx) {
            ctx.fillStyle = 'var(--via-color)';

            AppState.pcbWorkbench.vias.forEach(via => {
                ctx.beginPath();
                ctx.arc(via.x, via.y, 3, 0, 2 * Math.PI);
                ctx.fill();

                // Draw drill hole
                ctx.fillStyle = 'var(--pcb-green)';
                ctx.beginPath();
                ctx.arc(via.x, via.y, 1, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'var(--via-color)';
            });
        }

        function toggleLayer(layerName) {
            drawPCBCanvas();
        }

        function updateGrid() {
            drawPCBCanvas();
        }

        function autoRoute() {
            // Simple auto-routing algorithm
            AppState.pcbWorkbench.ratsnest.forEach(net => {
                if (!net.routed) {
                    const fromComp = AppState.pcbWorkbench.components.find(c => net.from.startsWith(c.id));
                    const toComp = AppState.pcbWorkbench.components.find(c => net.to.startsWith(c.id));

                    if (fromComp && toComp && fromComp.placed && toComp.placed) {
                        // Create simple straight trace
                        AppState.pcbWorkbench.traces.push({
                            id: `trace_${Date.now()}`,
                            layer: 'top',
                            points: [
                                { x: fromComp.x, y: fromComp.y },
                                { x: toComp.x, y: toComp.y }
                            ],
                            net: net.from + '-' + net.to
                        });

                        net.routed = true;
                    }
                }
            });

            drawPCBCanvas();
            updatePCBInfo();
        }

        function clearPCB() {
            AppState.pcbWorkbench.traces = [];
            AppState.pcbWorkbench.vias = [];
            AppState.pcbWorkbench.components.forEach(comp => {
                comp.placed = false;
                comp.x = 0;
                comp.y = 0;
            });
            AppState.pcbWorkbench.ratsnest.forEach(net => {
                net.routed = false;
            });

            populateComponentBin();
            drawPCBCanvas();
            updatePCBInfo();
            update3DPreview();
        }

        function updatePCBInfo() {
            const unroutedNets = AppState.pcbWorkbench.ratsnest.filter(net => !net.routed).length;
            const placedComponents = AppState.pcbWorkbench.components.filter(comp => comp.placed).length;
            const totalComponents = AppState.pcbWorkbench.components.length;
            const completion = totalComponents > 0 ? Math.round((placedComponents / totalComponents) * 100) : 0;

            document.getElementById('netsRemaining').textContent = unroutedNets;
            document.getElementById('pcbComponentCount').textContent = `${placedComponents}/${totalComponents}`;
            document.getElementById('traceCount').textContent = AppState.pcbWorkbench.traces.length;
            document.getElementById('viaCount').textContent = AppState.pcbWorkbench.vias.length;
            document.getElementById('completionPercent').textContent = completion + '%';

            // Update DRC status
            updateDRCStatus();
        }

        function updateDRCStatus() {
            const drcStatus = document.getElementById('drcStatus');
            const unroutedNets = AppState.pcbWorkbench.ratsnest.filter(net => !net.routed).length;

            if (unroutedNets === 0 && AppState.pcbWorkbench.components.every(comp => comp.placed)) {
                drcStatus.className = 'drc-status drc-pass';
                drcStatus.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 0.5rem;">✅ DRC: PASS</div>
                    <div style="font-size: 0.85rem;">All design rules satisfied</div>
                `;
                AppState.pcbWorkbench.drcStatus = 'pass';
            } else {
                drcStatus.className = 'drc-status drc-fail';
                drcStatus.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 0.5rem;">❌ DRC: FAIL</div>
                    <div style="font-size: 0.85rem;">Unrouted nets: ${unroutedNets}</div>
                `;
                AppState.pcbWorkbench.drcStatus = 'fail';
            }
        }

        function update3DPreview() {
            const canvas = document.getElementById('preview3DCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Simple 3D-like rendering
            ctx.fillStyle = '#2a2a2a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw PCB board in 3D perspective
            const boardWidth = canvas.width * 0.8;
            const boardHeight = canvas.height * 0.6;
            const offsetX = (canvas.width - boardWidth) / 2;
            const offsetY = (canvas.height - boardHeight) / 2;

            // PCB substrate
            ctx.fillStyle = 'var(--pcb-green)';
            ctx.fillRect(offsetX, offsetY, boardWidth, boardHeight);

            // Draw components in 3D
            AppState.pcbWorkbench.components.forEach(comp => {
                if (comp.placed) {
                    const x = offsetX + (comp.x / 800) * boardWidth;
                    const y = offsetY + (comp.y / 600) * boardHeight;

                    // Component shadow
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                    ctx.fillRect(x + 2, y + 2, 12, 8);

                    // Component body
                    const footprint = ComponentFootprints[comp.footprint];
                    ctx.fillStyle = footprint ? footprint.color : '#333';
                    ctx.fillRect(x, y, 10, 6);
                }
            });
        }

        // Export Functions
        function exportGerber() {
            const fileList = [
                'top_copper.gbr',
                'bottom_copper.gbr',
                'top_silkscreen.gto',
                'bottom_silkscreen.gbo',
                'drill_holes.drl',
                'board_outline.gko'
            ];

            document.getElementById('exportFileList').innerHTML = fileList.map(file =>
                `<div>📄 ${file}</div>`
            ).join('');

            document.getElementById('exportModal').classList.remove('hidden');
        }

        function exportBOM() {
            const bomData = AppState.pcbWorkbench.components.map(comp => ({
                designator: comp.id,
                footprint: comp.footprint,
                value: comp.properties.resistance || comp.properties.capacitance || comp.properties.gain || 'N/A'
            }));

            let csvContent = 'Designator,Footprint,Value\n';
            bomData.forEach(item => {
                csvContent += `${item.designator},${item.footprint},${item.value}\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'bill_of_materials.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function export3D() {
            alert('3D model export functionality would integrate with professional CAD software in a production environment.');
        }

        function closeExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        // Utility Functions
        function updateProceedToPCBButton() {
            const btn = document.getElementById('proceedToPCBBtn');
            if (AppState.circuitSimulation.readyForPCB && AppState.circuitSimulation.components.length > 0) {
                btn.disabled = false;
                btn.textContent = '🔧 Proceed to PCB Layout';
            } else {
                btn.disabled = true;
                btn.textContent = '🔧 Design Circuit First';
            }
        }

        function updateCircuitInfo() {
            document.getElementById('componentCount').textContent = AppState.circuitSimulation.components.length;
            document.getElementById('connectionCount').textContent = AppState.circuitSimulation.connections.length;
            updateProceedToPCBButton();
        }

        function showHelp() {
            document.getElementById('helpModal').classList.remove('hidden');
        }

        function hideHelp() {
            document.getElementById('helpModal').classList.add('hidden');
        }

        // Event Handlers
        function handleCanvasClick(event) {
            // Circuit canvas click handler
            console.log('Circuit canvas clicked');
        }

        function handleCanvasMouseDown(event) {
            // Circuit canvas mouse down handler
        }

        function handleCanvasMouseMove(event) {
            // Circuit canvas mouse move handler
        }

        function handleCanvasMouseUp(event) {
            // Circuit canvas mouse up handler
        }

        function handlePCBCanvasMouseDown(event) {
            // PCB canvas mouse down handler
        }

        function handlePCBCanvasMouseMove(event) {
            // PCB canvas mouse move handler
        }

        function handlePCBCanvasMouseUp(event) {
            // PCB canvas mouse up handler
        }

        function drawCircuitCanvas() {
            // Circuit canvas drawing function
            const canvas = document.getElementById('circuitCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function startOscilloscopeAnimation() {
            // Oscilloscope animation function
        }

        function clearOscilloscope() {
            // Clear oscilloscope function
        }

        function exportData() {
            // Export simulation data function
        }

        // Drag and Drop for PCB Components
        document.addEventListener('dragover', function(event) {
            event.preventDefault();
        });

        document.addEventListener('drop', function(event) {
            event.preventDefault();

            if (event.target.id === 'pcbCanvas') {
                const componentId = event.dataTransfer.getData('text/plain');
                const component = AppState.pcbWorkbench.components.find(c => c.id === componentId);

                if (component && !component.placed) {
                    const rect = event.target.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    const gridSize = parseFloat(document.getElementById('gridSize').value) * 96;
                    component.x = Math.round(x / gridSize) * gridSize;
                    component.y = Math.round(y / gridSize) * gridSize;
                    component.placed = true;

                    populateComponentBin();
                    drawPCBCanvas();
                    updatePCBInfo();
                    update3DPreview();
                }
            }

            // Remove dragging class
            document.querySelectorAll('.dragging').forEach(el => {
                el.classList.remove('dragging');
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }

            // Close modals when clicking outside
            if (event.target.id === 'helpModal') {
                hideHelp();
            }
            if (event.target.id === 'exportModal') {
                closeExportModal();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize simulation lab
            updateSensorGrid();
            updateTabContent();

            // Set up canvas elements
            const oscilloscope = document.getElementById('oscilloscope');
            if (oscilloscope) {
                oscilloscope.width = oscilloscope.offsetWidth;
                oscilloscope.height = oscilloscope.offsetHeight;
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (AppState.currentMode === 'design') {
                    initializeCircuitCanvas();
                } else if (AppState.currentMode === 'pcb') {
                    initializePCBCanvas();
                    update3DPreview();
                } else {
                    const oscilloscope = document.getElementById('oscilloscope');
                    if (oscilloscope) {
                        oscilloscope.width = oscilloscope.offsetWidth;
                        oscilloscope.height = oscilloscope.offsetHeight;
                    }
                }
            });

            // Initialize with simulation mode
            switchMode('simulation');

            // Initialize PCB tool selection
            selectPCBTool('select');
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case '1':
                        event.preventDefault();
                        switchMode('simulation');
                        break;
                    case '2':
                        event.preventDefault();
                        switchMode('design');
                        break;
                    case '3':
                        event.preventDefault();
                        switchMode('pcb');
                        break;
                    case 's':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            if (AppState.circuitSimulation.running) {
                                // stopCircuitSimulation();
                            } else {
                                // runCircuitSimulation();
                            }
                        }
                        break;
                    case 'c':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            // clearCanvas();
                        } else if (AppState.currentMode === 'pcb') {
                            clearPCB();
                        }
                        break;
                }
            }

            // Escape key to close modals and dropdowns
            if (event.key === 'Escape') {
                hideHelp();
                closeExportModal();
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }

            // Delete key to remove selected PCB components
            if (event.key === 'Delete' && AppState.currentMode === 'pcb') {
                // Remove selected component logic would go here
            }
        });

        // Additional helper functions for circuit simulation
        function runCircuitSimulation() {
            AppState.circuitSimulation.running = true;
            document.getElementById('runSimBtn').disabled = true;
            document.getElementById('stopSimBtn').disabled = false;
            document.getElementById('simStatus').textContent = 'Running';
            AppState.circuitSimulation.readyForPCB = true;
            updateProceedToPCBButton();
        }

        function stopCircuitSimulation() {
            AppState.circuitSimulation.running = false;
            document.getElementById('runSimBtn').disabled = false;
            document.getElementById('stopSimBtn').disabled = true;
            document.getElementById('simStatus').textContent = 'Stopped';
        }

        function generateBodePlot() {
            // Bode plot generation logic
            console.log('Generating Bode plot...');
        }

        function clearCanvas() {
            AppState.circuitSimulation.components = [];
            AppState.circuitSimulation.connections = [];
            AppState.circuitSimulation.readyForPCB = false;
            drawCircuitCanvas();
            updateCircuitInfo();
        }

        function showAnalysisTab(tabName) {
            // Update analysis tab buttons
            document.querySelectorAll('.analysis-tab').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';
        }

        function applyProperties() {
            // Apply component properties
        }

        function closeProperties() {
            // Close properties panel
        }

        // Console welcome message
        console.log(`
        🚀 Bio-Signal Explorer v4.0 Loaded Successfully!

        Features:
        • 🔬 Simulation Lab - Biomedical measurement simulation
        • ⚡ Circuit Design Toolkit - Interactive circuit design
        • 🔧 PCB Workbench - Complete PCB layout and design

        Keyboard Shortcuts:
        • Ctrl+1: Simulation Lab
        • Ctrl+2: Circuit Design
        • Ctrl+3: PCB Workbench
        • Ctrl+S: Start/Stop simulation
        • Escape: Close modals

        Developed by Dr. Mohammed Yagoub Esmail
        Sudan University of Science and Technology - BME Department
        `);
    </script>
</body>
</html>
