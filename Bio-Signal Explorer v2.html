<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v2.0: Enhanced Virtual Lab</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-dark: #1a202c;
            --panel-bg: #2d3748;
            --border-color: #4a5568;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --accent-cyan: #00e1ff;
            --accent-green: #48bb78;
            --accent-yellow: #f6e05e;
            --accent-red: #f56565;
            --lcd-bg: #171923;
            --lcd-text: var(--accent-cyan);
            --led-off: #4a5568;
            --led-on: var(--accent-green);
            --button-bg: #4299e1;
            --button-hover-bg: #3182ce;
            --button-disabled-bg: #718096;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            background-color: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .hidden { display: none !important; }
        .disabled { opacity: 0.5; pointer-events: none; }

        header {
            background-color: var(--panel-bg);
            color: var(--text-primary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-left h1 {
            margin: 0;
            font-size: 2rem;
        }

        .header-left p {
            margin: 0.25rem 0 0;
            font-size: 1rem;
            color: var(--text-secondary);
        }

        .header-right {
            display: flex;
            gap: 1rem;
        }

        .btn-home, .btn-help {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: var(--panel-bg);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-home:hover, .btn-help:hover {
            background: var(--hover-bg);
            border-color: var(--accent-cyan);
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            gap: 20px;
        }

        .control-panel, .display-area {
            background-color: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .control-panel { flex: 1; min-width: 350px; }
        .display-area { flex: 2; min-width: 500px; }

        h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 15px;
            font-size: 1.5rem;
            color: var(--text-primary);
        }

        .control-group { margin-bottom: 30px; }
        label { display: block; font-weight: 600; margin-bottom: 10px; color: var(--text-secondary); }

        select, button {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-size: 1rem;
            cursor: pointer;
            background-color: #4a5568;
            color: var(--text-primary);
            transition: all 0.2s ease-in-out;
        }
        select:focus, button:focus { outline: 2px solid var(--accent-cyan); }
        
        button {
            background-color: var(--button-bg);
            border: none;
            font-weight: 600;
        }
        button:not(:disabled):hover { background-color: var(--button-hover-bg); transform: translateY(-1px); }
        button:disabled { background-color: var(--button-disabled-bg); cursor: not-allowed; }
        
        #power-btn { background-color: var(--accent-green); }
        #power-btn.powered-on { background-color: var(--accent-red); }

        .sensor-list {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            gap: 12px;
        }
        .sensor-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: #1a202c;
        }
        .sensor-item input { margin-right: 10px; }
        .led {
            width: 16px;
            height: 16px;
            background-color: var(--led-off);
            border-radius: 50%;
            margin-left: auto;
            transition: all 0.3s ease;
            box-shadow: inset 0 0 4px rgba(0,0,0,0.5);
        }
        .led.led-on {
            background-color: var(--led-on);
            box-shadow: 0 0 10px var(--led-on), inset 0 0 3px white;
        }

        .slider-container { display: flex; align-items: center; gap: 15px; margin-bottom: 10px; }
        .slider-container input[type="range"] { flex-grow: 1; }
        .slider-container span { font-weight: 600; min-width: 80px; text-align: right; font-family: 'Roboto Mono', monospace; }
        .slider-label { min-width: 80px; text-align: left; }

        .safety-warning {
            border: 2px solid var(--accent-red);
            background-color: rgba(245, 101, 101, 0.1);
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }

        .virtual-lcd {
            background-color: var(--lcd-bg);
            color: var(--lcd-text);
            font-family: 'Roboto Mono', monospace;
            padding: 20px;
            border-radius: 8px;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            margin-bottom: 20px;
            font-size: 1.1rem;
            white-space: pre;
            border: 2px solid var(--border-color);
        }

        .oscilloscope {
            background-color: #000;
            border: 5px solid #333;
            border-radius: 5px;
            height: 400px;
            position: relative;
            overflow: hidden;
        }
        .oscilloscope-grid {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px);
            background-size: 10% 10%;
        }
        .oscilloscope canvas { width: 100%; height: 100%; position: absolute; top: 0; left: 0; }
        .status-message {
            background-color: rgba(246, 224, 94, 0.1);
            border-left: 4px solid var(--accent-yellow);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            color: var(--accent-yellow);
        }

        .tabs { margin-top: 20px; }
        .tab-buttons { display: flex; border-bottom: 1px solid var(--border-color); }
        .tab-button {
            padding: 10px 20px; cursor: pointer; border: none;
            background-color: transparent; font-size: 1rem;
            border-bottom: 3px solid transparent; color: var(--text-secondary);
            display: flex; align-items: center; gap: 8px;
        }
        .tab-button.active { font-weight: 600; color: var(--accent-cyan); border-bottom: 3px solid var(--accent-cyan); }
        .tab-content { padding: 20px; border: 1px solid var(--border-color); border-top: none; min-height: 150px; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; }
        
        .modal-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0, 0, 0, 0.7); display: flex;
            justify-content: center; align-items: center; z-index: 1000;
        }
        .modal-content {
            background-color: var(--panel-bg); padding: 30px; border-radius: 8px;
            width: 90%; max-width: 500px; position: relative; border: 1px solid var(--border-color);
        }
        .modal-close {
            position: absolute; top: 10px; right: 15px; font-size: 1.5rem;
            cursor: pointer; border: none; background: none; color: var(--text-secondary);
        }

        .help-content {
            max-height: 400px;
            overflow-y: auto;
        }

        .help-section {
            margin-bottom: 1.5rem;
        }

        .help-section h4 {
            color: var(--accent-cyan);
            margin-bottom: 0.5rem;
        }

        .help-section p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .help-section ul {
            color: var(--text-secondary);
            padding-left: 1.5rem;
        }

        .help-section li {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>

    <header>
        <div class="header-content">
            <div class="header-left">
                <h1>Bio-Signal Explorer v2.0</h1>
                <p>Enhanced Biomedical Instrumentation Simulation</p>
            </div>
            <div class="header-right">
                <button type="button" class="btn-home" onclick="window.location.href='index.html'">
                    🏠 Home
                </button>
                <button type="button" class="btn-help" onclick="showHelp()">
                    ❓ Help
                </button>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="control-panel">
            <h2>Controller Hub</h2>
            <div class="control-group">
                <button type="button" id="power-btn">Power On</button>
            </div>
            <div id="main-controls" class="disabled">
                <div class="control-group">
                    <label for="module-select">Select Measurement Module:</label>
                    <select id="module-select"></select>
                </div>
                <div class="control-group">
                    <label>Connect Sensors:</label>
                    <ul class="sensor-list" id="sensor-list"></ul>
                </div>
                <div class="control-group">
                    <label>Integrated Function Generator:</label>
                    <div class="slider-container">
                        <label for="freq-slider" class="slider-label">Frequency:</label>
                        <input type="range" id="freq-slider" min="1" max="1000" value="50">
                        <span id="freq-value">50 Hz</span>
                    </div>
                    <div class="slider-container">
                        <label for="amp-slider" class="slider-label">Amplitude:</label>
                        <input type="range" id="amp-slider" min="0.1" max="10" value="1" step="0.1">
                        <span id="amp-value">1.0 Vpp</span>
                    </div>
                </div>
                <div class="control-group">
                    <button type="button" id="analysis-mode-btn" disabled>Enter Circuit Analysis Mode</button>
                </div>
                <div id="safety-warning" class="safety-warning hidden">
                    CAUTION! People with cardiac pacemakers must avoid using this.
                </div>
            </div>
        </div>

        <div class="display-area">
            <h2>System Display</h2>
            <div id="status-message" class="status-message">System is powered off.</div>
            <div class="virtual-lcd" id="virtual-lcd"></div>
            <div class="oscilloscope" id="oscilloscope">
                <div class="oscilloscope-grid"></div>
                <canvas id="scope-canvas"></canvas>
            </div>
            <div class="tabs">
                <div class="tab-buttons" id="tab-buttons"></div>
                <div class="tab-content" id="tab-content-area"></div>
            </div>
        </div>
    </main>

    <!-- Help Modal -->
    <div id="help-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <button type="button" class="modal-close" onclick="hideHelp()">×</button>
            <h3>Bio-Signal Explorer v2.0 - Help Guide</h3>
            <div class="help-content">
                <div class="help-section">
                    <h4>Getting Started</h4>
                    <p>Welcome to the Bio-Signal Explorer Virtual Lab! This interactive platform simulates biomedical instrumentation for educational purposes.</p>
                    <ul>
                        <li>Click "Power On" to start the system</li>
                        <li>Select a measurement module from the dropdown</li>
                        <li>Connect the required sensors by checking the appropriate boxes</li>
                        <li>Observe the real-time signal visualization</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Available Modules</h4>
                    <ul>
                        <li><strong>ECG Measurement:</strong> Electrocardiogram for heart activity monitoring</li>
                        <li><strong>EMG Measurement:</strong> Electromyography for muscle activity analysis</li>
                        <li><strong>EEG Measurement:</strong> Electroencephalography for brain wave monitoring</li>
                        <li><strong>EOG Measurement:</strong> Electrooculography for eye movement tracking</li>
                        <li><strong>Body Impedance:</strong> Bioelectrical impedance analysis</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Circuit Analysis Mode</h4>
                    <p>Switch to circuit analysis mode to study filter characteristics and frequency response:</p>
                    <ul>
                        <li>Adjust frequency and amplitude using the sliders</li>
                        <li>Observe input vs output waveforms</li>
                        <li>View real-time gain calculations</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Data Export</h4>
                    <p>Export measurement data for further analysis:</p>
                    <ul>
                        <li>Navigate to the "Data Output" tab</li>
                        <li>Click "Download Sample Data" to get CSV files</li>
                        <li>Use the data for offline analysis and research</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Safety Information</h4>
                    <p><strong>Important:</strong> This is a virtual simulation for educational purposes only. Always follow proper safety protocols when working with real biomedical equipment.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- STATE & DATA ---
            const state = {
                powerOn: false,
                currentModule: null,
                analysisMode: false,
                animationFrameId: null,
                waveformAnimation: {
                    progress: 0,
                    speed: 0.005
                }
            };

            const labModules = {
                'ECG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN5'],
                    lcdOutput: 'Heart Rate: 72 BPM',
                    waveformPath: [[0,0],[0.15,0.1],[0.25,-0.1],[0.3,1.8],[0.35,-0.8],[0.45,0],[0.7,0.4],[0.9,0],[1,0]],
                    scopeChannels: 1, dualChannel: false,
                    principleText: 'The electrocardiogram (ECG) measures the electrical activity of the heart. Action potentials spreading through the myocardium create electrical currents that can be detected on the body surface, representing the cardiac cycle of depolarization and repolarization.',
                    procedureText: '1. Clean skin areas on both arms and right leg. \n2. Attach electrodes to RA (IN1), LA (IN2), LL (IN3), and RL (IN5). \n3. Ask the subject to relax and remain still. \n4. Select the desired lead (e.g., Lead I) and observe the P-QRS-T waveform.',
                    filterCutoff: { low: 0.1, high: 100 },
                    statusMessage: 'ECG Module selected. Please connect the required sensors to begin measurement.'
                },
                'EMG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN5'],
                    lcdOutput: 'Muscle Force: 2.5 V',
                    waveformPath: Array.from({length: 100}, (_, i) => [i/100, (Math.random() - 0.5) * 1.5]),
                    scopeChannels: 2, dualChannel: true,
                    principleText: 'Electromyography (EMG) records the electrical potential generated by muscle cells when these cells are electrically or neurologically activated. The signal represents the recruitment of motor units during muscle contraction.',
                    procedureText: '1. Place two electrodes over the belly of the target muscle (e.g., biceps). \n2. Place the ground electrode (IN5) on a bony prominence nearby. \n3. Record EMG during both isometric (static) and isotonic (dynamic) contractions.',
                    filterCutoff: { low: 100, high: 1000 },
                    statusMessage: 'EMG Module selected. Connect sensors to measure muscle activity.'
                },
                'EOG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4', 'IN5'],
                    lcdOutput: 'Eye Movement Detected',
                    waveformPath: [[0,0],[0.2,0],[0.3,1.5],[0.7,1.5],[0.8,0],[1,0]],
                    scopeChannels: 2, dualChannel: true,
                    principleText: 'The electrooculogram (EOG) measures the standing potential between the front and back of the human eye. This potential allows for the tracking of eye movements, as the signal changes when the eye moves left/right or up/down.',
                    procedureText: '1. Place electrodes for horizontal (IN1, IN2) and vertical (IN3, IN4) eye movements. \n2. Place ground electrode (IN5) on the forehead. \n3. Ask the subject to follow a target with their eyes without moving their head.',
                    filterCutoff: { low: 0, high: 30 },
                    statusMessage: 'EOG Module selected. Connect sensors to track eye movement.'
                },
                'Body Impedance': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                    lcdOutput: 'Impedance: 500 Ω',
                    waveformPath: [[0,0.5],[0.25,1],[0.5,0.5],[0.75,0],[1,0.5]],
                    scopeChannels: 1, dualChannel: false,
                    principleText: 'Bioelectrical Impedance Analysis (BIA) measures the opposition to the flow of a small, high-frequency alternating current through the body. Changes in impedance can be correlated with changes in fluid volume, such as those caused by respiration or the cardiac cycle.',
                    procedureText: '1. Connect four electrodes to the subject (e.g., on hand and foot). \n2. Two electrodes (IN1, IN4) inject the current, two (IN2, IN3) measure the voltage drop. \n3. Observe impedance changes during breathing.',
                    filterCutoff: { low: 0.1, high: 30 },
                    statusMessage: 'Body Impedance Module selected. Connect sensors to measure tissue impedance.'
                }
                // Add other modules here for full functionality
            };

            // --- DOM ELEMENTS ---
            const powerBtn = document.getElementById('power-btn');
            const mainControls = document.getElementById('main-controls');
            const moduleSelect = document.getElementById('module-select');
            const sensorList = document.getElementById('sensor-list');
            const freqSlider = document.getElementById('freq-slider');
            const ampSlider = document.getElementById('amp-slider');
            const freqValue = document.getElementById('freq-value');
            const ampValue = document.getElementById('amp-value');
            const analysisModeBtn = document.getElementById('analysis-mode-btn');
            const safetyWarning = document.getElementById('safety-warning');
            const statusMessage = document.getElementById('status-message');
            const virtualLcd = document.getElementById('virtual-lcd');
            const scopeCanvas = document.getElementById('scope-canvas');
            const tabButtonsContainer = document.getElementById('tab-buttons');
            const tabContentArea = document.getElementById('tab-content-area');

            // --- INITIALIZATION ---
            function initialize() {
                powerBtn.addEventListener('click', togglePower);
                moduleSelect.addEventListener('change', selectModule);
                freqSlider.addEventListener('input', updateFuncGenDisplay);
                ampSlider.addEventListener('input', updateFuncGenDisplay);
                analysisModeBtn.addEventListener('click', toggleAnalysisMode);

                populateDropdown();
                populateSensors();
                setupTabs();
                updateUIforPowerState();
            }

            function populateDropdown() {
                Object.keys(labModules).forEach(key => {
                    moduleSelect.add(new Option(key, key));
                });
            }

-            function populateSensors() {
                for (let i = 1; i <= 7; i++) {
                    const li = document.createElement('li');
                    li.className = 'sensor-item';
                    li.innerHTML = `<input type="checkbox" id="sensor-in${i}" data-sensor="IN${i}"><label for="sensor-in${i}">IN${i}</label><span class="led" id="led-in${i}"></span>`;
                    sensorList.appendChild(li);
                    li.querySelector('input').addEventListener('change', checkSensorLEDs);
                }
            }

            function setupTabs() {
                const tabs = {
                    principle: { name: 'Principle', icon: 'M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z' },
                    procedure: { name: 'Procedure', icon: 'M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5' },
                    data: { name: 'Data Output', icon: 'M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3' }
                };
                let buttonsHTML = '';
                Object.keys(tabs).forEach((key, index) => {
                    buttonsHTML += `<button class="tab-button ${index === 0 ? 'active' : ''}" data-tab="${key}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="20" height="20"><path stroke-linecap="round" stroke-linejoin="round" d="${tabs[key].icon}" /></svg>
                        ${tabs[key].name}
                    </button>`;
                });
                tabButtonsContainer.innerHTML = buttonsHTML;
                tabButtonsContainer.querySelectorAll('.tab-button').forEach(btn => btn.addEventListener('click', switchTab));
            }

            // --- EVENT HANDLERS ---
            function togglePower() {
                state.powerOn = !state.powerOn;
                updateUIforPowerState();
            }

            function selectModule() {
                state.currentModule = moduleSelect.value;
                if (state.analysisMode) toggleAnalysisMode(); // Exit analysis mode
                updateFullDisplay();
            }

            function switchTab(e) {
                const targetTab = e.currentTarget.dataset.tab;
                tabButtonsContainer.querySelectorAll('.tab-button').forEach(btn => btn.classList.toggle('active', btn.dataset.tab === targetTab));
                updateTabContent();
            }

            function toggleAnalysisMode() {
                state.analysisMode = !state.analysisMode;
                analysisModeBtn.textContent = state.analysisMode ? 'Exit Circuit Analysis' : 'Enter Circuit Analysis';
                analysisModeBtn.style.backgroundColor = state.analysisMode ? 'var(--accent-yellow)' : 'var(--button-bg)';
                analysisModeBtn.style.color = state.analysisMode ? 'var(--bg-dark)' : 'var(--text-primary)';
                startStopAnimation();
            }

            // --- UI UPDATE FUNCTIONS ---
            function updateUIforPowerState() {
                powerBtn.textContent = state.powerOn ? 'Power Off' : 'Power On';
                powerBtn.classList.toggle('powered-on', state.powerOn);
                mainControls.classList.toggle('disabled', !state.powerOn);
                if (state.powerOn) {
                    selectModule();
                } else {
                    state.currentModule = null;
                    if (state.analysisMode) toggleAnalysisMode();
                    updateFullDisplay();
                }
            }

            function updateFullDisplay() {
                const moduleKey = state.currentModule;
                const moduleData = moduleKey ? labModules[moduleKey] : null;

                updateStatusMessage(moduleData);
                updateLCD(moduleData);
                updateTabContent();
                updateSafetyWarning(moduleKey);
                checkSensorLEDs();
                analysisModeBtn.disabled = !moduleKey;
                startStopAnimation();
            }

            function updateStatusMessage(moduleData) {
                if (!state.powerOn) {
                    statusMessage.textContent = 'System is powered off.';
                    statusMessage.style.display = 'block';
                } else if (moduleData) {
                    statusMessage.textContent = moduleData.statusMessage;
                    statusMessage.style.display = 'block';
                } else {
                    statusMessage.style.display = 'none';
                }
            }

            function updateLCD(moduleData) {
                if (!state.powerOn || !moduleData) {
                    virtualLcd.innerHTML = '';
                    return;
                }
                const freq = freqSlider.value;
                const amp = parseFloat(ampSlider.value).toFixed(1);
                virtualLcd.innerHTML = `<span>INPUT: ${state.currentModule}</span><span>FUNC GEN: ${freq.padStart(4, ' ')}Hz ${amp.padStart(4, ' ')}Vpp</span><span>OUTPUT: ${moduleData.lcdOutput}</span>`;
            }

            function updateFuncGenDisplay() {
                if (!state.powerOn) return;
                freqValue.textContent = `${freqSlider.value} Hz`;
                ampValue.textContent = `${parseFloat(ampSlider.value).toFixed(1)} Vpp`;
                updateLCD(labModules[state.currentModule]);
            }

            function updateTabContent() {
                if (!state.powerOn || !state.currentModule) {
                    tabContentArea.innerHTML = '';
                    return;
                }
                const activeTab = tabButtonsContainer.querySelector('.active').dataset.tab;
                const moduleData = labModules[state.currentModule];
                let content = '';
                switch (activeTab) {
                    case 'principle':
                        content = `<h3>Principle</h3><p>${moduleData.principleText}</p>`;
                        break;
                    case 'procedure':
                        content = `<h3>Procedure</h3><p style="white-space: pre-wrap;">${moduleData.procedureText}</p>`;
                        break;
                    case 'data':
                        content = `<h3>Data Output Simulation</h3>
                                   <p>Analog Outputs Active: ${moduleData.scopeChannels}</p>
                                   <p>Digital Interface: RS232 @ 9600 Baud</p>
                                   <button id="download-btn">Download Sample Data (.csv)</button>`;
                        break;
                }
                tabContentArea.innerHTML = content;
                if (activeTab === 'data') {
                    document.getElementById('download-btn').addEventListener('click', downloadData);
                }
            }

            function updateSafetyWarning(moduleKey) {
                safetyWarning.classList.toggle('hidden', moduleKey !== 'Body Impedance');
            }

            function checkSensorLEDs() {
                const required = state.currentModule ? labModules[state.currentModule].requiredSensors : [];
                for (let i = 1; i <= 7; i++) {
                    const checkbox = document.getElementById(`sensor-in${i}`);
                    const led = document.getElementById(`led-in${i}`);
                    led.classList.toggle('led-on', state.powerOn && required.includes(`IN${i}`) && checkbox.checked);
                }
            }

            function downloadData() {
                const moduleData = labModules[state.currentModule];
                let csvContent = "data:text/csv;charset=utf-8,Time (s),Voltage (V)\n";
                if (moduleData.dualChannel) {
                    csvContent = "data:text/csv;charset=utf-8,Time (s),Voltage Ch1 (V),Voltage Ch2 (V)\n";
                }

                for (let i = 0; i < 100; i++) {
                    const time = (i * 0.01).toFixed(3);
                    const voltage1 = (Math.random() * 2 - 1).toFixed(4);
                    if (moduleData.dualChannel) {
                        const voltage2 = (Math.random() * 2 - 1).toFixed(4);
                        csvContent += `${time},${voltage1},${voltage2}\n`;
                    } else {
                        csvContent += `${time},${voltage1}\n`;
                    }
                }
                
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `${state.currentModule.replace(/\s+/g, '_')}_data.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // --- RENDERING & ANIMATION ---
            function startStopAnimation() {
                if (state.animationFrameId) cancelAnimationFrame(state.animationFrameId);
                if (state.powerOn && state.currentModule) {
                    const rect = scopeCanvas.parentElement.getBoundingClientRect();
                    scopeCanvas.width = rect.width;
                    scopeCanvas.height = rect.height;
                    state.waveformAnimation.progress = 0;
                    state.animationFrameId = requestAnimationFrame(animate);
                } else {
                    const ctx = scopeCanvas.getContext('2d');
                    ctx.clearRect(0, 0, scopeCanvas.width, scopeCanvas.height);
                }
            }

            function animate() {
                if (state.analysisMode) {
                    drawCircuitWaves();
                } else {
                    animatePhysiologicalWaveform();
                }
                state.animationFrameId = requestAnimationFrame(animate);
            }

            function animatePhysiologicalWaveform() {
                const moduleData = labModules[state.currentModule];
                if (!moduleData) return;

                const ctx = scopeCanvas.getContext('2d');
                const w = scopeCanvas.width;
                const h = scopeCanvas.height;
                
                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'; // Fading trail effect
                ctx.fillRect(0, 0, w, h);

                state.waveformAnimation.progress += state.waveformAnimation.speed;
                if (state.waveformAnimation.progress > 1) state.waveformAnimation.progress = 0;

                const path = moduleData.waveformPath;
                const y_scale = h / 4; // Scale waveform amplitude
                const y_offset = h / 2;

                function drawTrace(y_center, color) {
                    ctx.beginPath();
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2;
                    ctx.shadowBlur = 5;
                    ctx.shadowColor = color;

                    for (let i = 0; i < path.length - 1; i++) {
                        const t_start = path[i][0];
                        const t_end = path[i+1][0];
                        
                        if (state.waveformAnimation.progress >= t_start && state.waveformAnimation.progress < t_end) {
                            const segment_progress = (state.waveformAnimation.progress - t_start) / (t_end - t_start);
                            const x1 = t_start * w;
                            const y1 = y_center - path[i][1] * y_scale;
                            const x2 = (t_start + (t_end - t_start) * segment_progress) * w;
                            const y2 = y_center - (path[i][1] + (path[i+1][1] - path[i][1]) * segment_progress) * y_scale;
                            
                            ctx.moveTo(x1, y1);
                            ctx.lineTo(x2, y2);
                        } else if (state.waveformAnimation.progress >= t_end) {
                            const x1 = t_start * w;
                            const y1 = y_center - path[i][1] * y_scale;
                            const x2 = t_end * w;
                            const y2 = y_center - path[i+1][1] * y_scale;
                            ctx.moveTo(x1, y1);
                            ctx.lineTo(x2, y2);
                        }
                    }
                    ctx.stroke();
                    ctx.shadowBlur = 0;
                }

                drawTrace(moduleData.dualChannel ? h * 0.25 : h * 0.5, 'var(--accent-cyan)');
                if (moduleData.dualChannel) {
                    drawTrace(h * 0.75, 'var(--accent-yellow)');
                }
            }

            function drawCircuitWaves() {
                const ctx = scopeCanvas.getContext('2d');
                const w = scopeCanvas.width;
                const h = scopeCanvas.height;
                const midY = h / 2;

                const freq = parseFloat(freqSlider.value);
                const inputAmp = parseFloat(ampSlider.value);
                const moduleData = labModules[state.currentModule];
                const { low: lowCutoff, high: highCutoff } = moduleData.filterCutoff;

                const highPassGain = 1 / Math.sqrt(1 + Math.pow(lowCutoff / freq, 2));
                const lowPassGain = 1 / Math.sqrt(1 + Math.pow(freq / highCutoff, 2));
                const totalGain = highPassGain * lowPassGain;
                const outputAmp = inputAmp * totalGain;

                ctx.clearRect(0, 0, w, h);

                // Draw Input Wave
                ctx.beginPath();
                ctx.strokeStyle = 'var(--accent-cyan)';
                ctx.lineWidth = 2;
                for (let x = 0; x < w; x++) {
                    const angle = (x / w) * Math.PI * 2 * (freq / 50);
                    const y = midY - (Math.sin(angle) * (inputAmp / 10) * (midY * 0.8));
                    x === 0 ? ctx.moveTo(x, y) : ctx.lineTo(x, y);
                }
                ctx.stroke();

                // Draw Output Wave
                ctx.beginPath();
                ctx.strokeStyle = 'var(--accent-yellow)';
                ctx.lineWidth = 2;
                for (let x = 0; x < w; x++) {
                    const angle = (x / w) * Math.PI * 2 * (freq / 50);
                    const y = midY - (Math.sin(angle) * (outputAmp / 10) * (midY * 0.8));
                    x === 0 ? ctx.moveTo(x, y) : ctx.lineTo(x, y);
                }
                ctx.stroke();
                
                // Display Gain
                ctx.font = "600 16px 'Inter', sans-serif";
                ctx.fillStyle = 'white';
                ctx.textAlign = 'right';
                ctx.fillText(`Gain: ${totalGain.toFixed(3)}`, w - 20, 30);
            }

            // --- START THE APP ---
            initialize();
        });

        // Help modal functions
        function showHelp() {
            document.getElementById('help-modal').classList.remove('hidden');
        }

        function hideHelp() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const helpModal = document.getElementById('help-modal');
            if (e.target === helpModal) {
                hideHelp();
            }
        });
    </script>

</body>
</html>
