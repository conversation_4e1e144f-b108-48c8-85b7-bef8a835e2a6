/* Bio-Signal Explorer v2.0 - Main Stylesheet */

:root {
    --primary-bg: #0f172a;
    --secondary-bg: #1e293b;
    --accent-blue: #3b82f6;
    --accent-cyan: #06b6d4;
    --accent-green: #10b981;
    --accent-purple: #8b5cf6;
    --accent-yellow: #f6e05e;
    --accent-red: #f56565;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #334155;
    --card-bg: #1e293b;
    --hover-bg: #334155;
    --panel-bg: #2d3748;
    --lcd-bg: #171923;
    --lcd-text: #00e1ff;
    --led-off: #4a5568;
    --led-on: #48bb78;
    --button-bg: #4299e1;
    --button-hover-bg: #3182ce;
    --button-disabled-bg: #718096;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Utility Classes */
.hidden { 
    display: none !important; 
}

.disabled { 
    opacity: 0.5; 
    pointer-events: none; 
}

.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Header Styles */
header {
    background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-left p {
    font-size: 1.2rem;
    opacity: 0.95;
    margin-bottom: 1rem;
}

.header-right {
    display: flex;
    gap: 1rem;
}

.author-info {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 0.75rem;
    font-size: 0.95rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Navigation Styles */
.nav-menu {
    background: var(--card-bg);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.nav-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--text-primary);
    background: var(--hover-bg);
}

/* Button Styles */
.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-cyan));
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--hover-bg);
    border-color: var(--accent-cyan);
}

.btn-home, .btn-help {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--panel-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-home:hover, .btn-help:hover {
    background: var(--hover-bg);
    border-color: var(--accent-cyan);
}

/* Container and Layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

.hero-section {
    text-align: center;
    margin-bottom: 4rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Card Styles */
.feature-card, .module-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-cyan));
}

.feature-card:hover, .module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: var(--accent-cyan);
}

.feature-icon, .module-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-cyan));
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.module-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(45deg, var(--accent-green), var(--accent-cyan));
    border-radius: 0.5rem;
    font-size: 1.2rem;
}

/* Grid Layouts */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

/* Footer Styles */
.footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 3rem 2rem 2rem;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.footer-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-description {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-cyan);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-left h1 {
        font-size: 2rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .features-grid,
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
}
