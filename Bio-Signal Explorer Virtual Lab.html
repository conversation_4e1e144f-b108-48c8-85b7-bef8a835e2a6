
Generated html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer: Virtual Lab</title>
    <style>
        :root {
            --primary-bg: #f4f7f6;
            --control-panel-bg: #ffffff;
            --display-area-bg: #e9ecef;
            --header-bg: #2c3e50;
            --header-text: #ecf0f1;
            --text-color: #34495e;
            --border-color: #ced4da;
            --accent-color: #3498db;
            --lcd-bg: #2d3a40;
            --lcd-text: #9ff4a2;
            --led-off: #adb5bd;
            --led-on: #28a745;
            --warning-bg: #fff3cd;
            --warning-border: #f39c12;
            --button-bg: #3498db;
            --button-hover-bg: #2980b9;
            --button-disabled-bg: #95a5a6;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            background-color: var(--primary-bg);
            color: var(--text-color);
            line-height: 1.6;
        }

        .hidden {
            display: none !important;
        }

        header {
            background-color: var(--header-bg);
            color: var(--header-text);
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2rem;
        }

        header p {
            margin: 0.25rem 0 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            gap: 20px;
        }

        .control-panel, .display-area {
            background-color: var(--control-panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .control-panel {
            flex: 1;
            min-width: 320px;
        }

        .display-area {
            flex: 2;
            min-width: 450px;
        }

        h2 {
            margin-top: 0;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
            font-size: 1.25rem;
        }

        .control-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
        }

        select, button {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            font-size: 1rem;
            cursor: pointer;
        }
        
        button {
            background-color: var(--button-bg);
            color: white;
            border: none;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: var(--button-hover-bg);
        }
        
        button:disabled {
            background-color: var(--button-disabled-bg);
            cursor: not-allowed;
        }

        .sensor-list {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .sensor-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
        }

        .sensor-item input {
            margin-right: 10px;
        }

        .led {
            width: 15px;
            height: 15px;
            background-color: var(--led-off);
            border-radius: 50%;
            margin-left: auto;
            transition: background-color 0.3s;
            box-shadow: inset 0 0 3px rgba(0,0,0,0.3);
        }

        .led.led-on {
            background-color: var(--led-on);
            box-shadow: 0 0 8px var(--led-on);
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider-container input[type="range"] {
            flex-grow: 1;
        }

        .slider-container span {
            font-weight: bold;
            min-width: 80px;
            text-align: right;
        }

        .safety-warning {
            border: 2px solid var(--warning-border);
            background-color: var(--warning-bg);
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }

        .virtual-lcd {
            background-color: var(--lcd-bg);
            color: var(--lcd-text);
            font-family: 'Courier New', Courier, monospace;
            padding: 15px;
            border-radius: 5px;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            margin-bottom: 20px;
            font-size: 1.1rem;
            white-space: pre;
        }

        .oscilloscope {
            background-color: #000;
            border: 5px solid #333;
            border-radius: 5px;
            height: 350px;
            position: relative;
            overflow: hidden;
        }
        
        .oscilloscope-grid {
            position: absolute;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 0, 0.15) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.15) 1px, transparent 1px);
            background-size: 10% 10%;
        }
        
        .oscilloscope-axes {
            position: absolute;
            color: rgba(0, 255, 0, 0.5);
            font-size: 0.8rem;
        }
        .x-axis-label { bottom: 5px; left: 5px; }
        .y-axis-label { top: 5px; left: 5px; }

        .oscilloscope img, .oscilloscope canvas {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0; left: 0;
        }

        .tabs {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid var(--border-color);
        }

        .tab-button {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 1rem;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            font-weight: bold;
            color: var(--accent-color);
            border-bottom: 3px solid var(--accent-color);
        }

        .tab-content {
            padding: 20px;
            border: 1px solid var(--border-color);
            border-top: none;
            min-height: 150px;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }
        
        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1.5rem;
            cursor: pointer;
            border: none;
            background: none;
        }
        
        .modal-content table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .modal-content th, .modal-content td {
            border: 1px solid var(--border-color);
            padding: 8px;
            text-align: center;
        }
        
        .modal-content th {
            background-color: var(--primary-bg);
        }

        @media (max-width: 900px) {
            .container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>

    <header>
        <h1>Bio-Signal Explorer: Virtual Lab</h1>
        <p>Biomedical Instrumentation Simulation for BME Students</p>
    </header>

    <main class="container">
        <div class="control-panel">
            <h2>Controller Hub</h2>
            <div class="control-group">
                <label for="module-select">Select Measurement Module:</label>
                <select id="module-select"></select>
            </div>

            <div class="control-group">
                <label>Connect Sensors:</label>
                <ul class="sensor-list" id="sensor-list">
                    <!-- Sensor items will be generated by JS -->
                </ul>
            </div>

            <div class="control-group">
                <label>Integrated Function Generator:</label>
                <div class="slider-container">
                    <label for="freq-slider" style="min-width: 80px; text-align: left;">Frequency:</label>
                    <input type="range" id="freq-slider" min="1" max="1000" value="50">
                    <span id="freq-value">50 Hz</span>
                </div>
                <div class="slider-container">
                    <label for="amp-slider" style="min-width: 80px; text-align: left;">Amplitude:</label>
                    <input type="range" id="amp-slider" min="0.1" max="10" value="1" step="0.1">
                    <span id="amp-value">1.0 Vpp</span>
                </div>
            </div>
            
            <div class="control-group">
                <button id="analysis-mode-btn" disabled>Enter Circuit Analysis Mode</button>
            </div>

            <div id="safety-warning" class="safety-warning hidden">
                CAUTION! People with cardiac pacemakers must avoid using this.
            </div>
        </div>

        <div class="display-area">
            <h2>System Display</h2>
            <div class="virtual-lcd" id="virtual-lcd">
                <!-- LCD content will be generated by JS -->
            </div>

            <div class="oscilloscope" id="oscilloscope">
                <div class="oscilloscope-grid"></div>
                <div class="oscilloscope-axes x-axis-label">X-axis: TIME/DIV</div>
                <div class="oscilloscope-axes y-axis-label">Y-axis: VOLT/DIV</div>
                <img id="scope-img" src="" alt="Physiological Waveform">
                <canvas id="scope-canvas" class="hidden"></canvas>
            </div>

            <div class="tabs">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="principle">Physiological Principle</button>
                    <button class="tab-button" data-tab="procedure">Procedure</button>
                    <button class="tab-button" data-tab="data-output">Data Output</button>
                </div>
                <div class="tab-content" id="tab-principle"></div>
                <div class="tab-content hidden" id="tab-procedure"></div>
                <div class="tab-content hidden" id="tab-data-output"></div>
            </div>
        </div>
    </main>
    
    <div id="data-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <button id="modal-close-btn" class="modal-close">×</button>
            <h3>Simulated RS232 Data Download</h3>
            <p>This is a sample of the data that would be exported to an .XLS file.</p>
            <table id="data-table">
                <!-- Table content will be generated by JS -->
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DATA OBJECT: SINGLE SOURCE OF TRUTH ---
            const labModules = {
                'ECG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN5'],
                    lcdOutput: 'Heart Rate: 72 BPM',
                    scopeWaveform: 'https://i.imgur.com/6XyqL5p.png', // Placeholder image
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'The electrocardiogram (ECG) measures the electrical activity of the heart. Action potentials spreading through the myocardium create electrical currents that can be detected on the body surface, representing the cardiac cycle of depolarization and repolarization.',
                    procedureText: '1. Clean skin areas on both arms and right leg. \n2. Attach electrodes to RA (IN1), LA (IN2), LL (IN3), and RL (IN5). \n3. Ask the subject to relax and remain still. \n4. Select the desired lead (e.g., Lead I) and observe the P-QRS-T waveform.',
                    filterCutoff: { low: 0.1, high: 100 }
                },
                'EMG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN5'],
                    lcdOutput: 'Muscle Force: 2.5 V',
                    scopeWaveform: 'https://i.imgur.com/oN8bU2r.png',
                    scopeWaveformDual: 'https://i.imgur.com/oN8bU2r.png', // Using same for demo
                    scopeChannels: 2,
                    principleText: 'Electromyography (EMG) records the electrical potential generated by muscle cells when these cells are electrically or neurologically activated. The signal represents the recruitment of motor units during muscle contraction.',
                    procedureText: '1. Place two electrodes over the belly of the target muscle (e.g., biceps). \n2. Place the ground electrode (IN5) on a bony prominence nearby. \n3. Record EMG during both isometric (static) and isotonic (dynamic) contractions.',
                    filterCutoff: { low: 100, high: 1000 }
                },
                'EOG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4', 'IN5'],
                    lcdOutput: 'Eye Movement Detected',
                    scopeWaveform: 'https://i.imgur.com/9s7fJ3t.png',
                    scopeWaveformDual: 'https://i.imgur.com/9s7fJ3t.png', // Demo image for dual channel
                    scopeChannels: 2,
                    principleText: 'The electrooculogram (EOG) measures the standing potential between the front and back of the human eye. This potential allows for the tracking of eye movements, as the signal changes when the eye moves left/right or up/down.',
                    procedureText: '1. Place electrodes for horizontal (IN1, IN2) and vertical (IN3, IN4) eye movements. \n2. Place ground electrode (IN5) on the forehead. \n3. Ask the subject to follow a target with their eyes without moving their head.',
                    filterCutoff: { low: 0, high: 30 }
                },
                'EEG Measurement': {
                    requiredSensors: ['IN1', 'IN2', 'IN5'],
                    lcdOutput: 'Alpha Wave: 10 Hz',
                    scopeWaveform: 'https://i.imgur.com/r3c4v5e.png',
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'Electroencephalography (EEG) records the electrical activity of the brain from the scalp. These signals, originating from synaptic potentials in cortical neurons, are categorized into frequency bands (alpha, beta, etc.) that correlate with different mental states.',
                    procedureText: '1. Place electrodes on the scalp according to the 10-20 system (e.g., Fp1, Fp2). \n2. Place ground electrode (IN5) on an earlobe. \n3. Record EEG with eyes open and eyes closed to observe the effect on alpha wave activity.',
                    filterCutoff: { low: 1, high: 20 }
                },
                'Oscillometric Blood Pressure': {
                    requiredSensors: ['IN6'],
                    lcdOutput: 'BP: 120/80 mmHg',
                    scopeWaveform: 'https://i.imgur.com/uJvK7wF.png',
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'The oscillometric method measures blood pressure by detecting the oscillations in pressure within a cuff. The point of maximum oscillation corresponds to the Mean Arterial Pressure, from which systolic and diastolic pressures are calculated.',
                    procedureText: '1. Wrap the cuff around the upper arm. \n2. Connect the pressure sensor to IN6. \n3. Inflate the cuff above systolic pressure. \n4. Slowly deflate the cuff and record the pressure oscillations.',
                    filterCutoff: { low: 0.3, high: 3 }
                },
                'Vessel Volume Measurement': {
                    requiredSensors: ['IN7'],
                    lcdOutput: 'Heart Rate: 68 BPM',
                    scopeWaveform: 'https://i.imgur.com/6XyqL5p.png', // Similar to ECG
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'Photoplethysmography (PPG) is an optical technique used to detect blood volume changes in the microvascular bed of tissue. An infrared photo-coupler measures changes in light absorption, which correspond to the pulse.',
                    procedureText: '1. Connect the photo-coupler sensor to IN7. \n2. Place the sensor on a fingertip or earlobe. \n3. Ensure the subject is still to get a clear pulsatile signal.',
                    filterCutoff: { low: 1, high: 40 }
                },
                'Respiratory Frequency': {
                    requiredSensors: ['IN4', 'IN5'],
                    lcdOutput: 'Resp. Rate: 16/min',
                    scopeWaveform: 'https://i.imgur.com/d2fG8hH.png',
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'This method uses a thermal sensor (thermistor) to detect changes in air temperature during breathing. Inhaled air is cooler than exhaled air, creating a cyclical temperature change that corresponds to the respiratory rate.',
                    procedureText: '1. Connect the thermal sensor to IN4 and IN5. \n2. Position the sensor near the nose or mouth. \n3. Ask the subject to breathe normally.',
                    filterCutoff: { low: 0.1, high: 2 } // Example filter
                },
                'Pulse Measurement': {
                    requiredSensors: ['IN4', 'IN5'],
                    lcdOutput: 'Heart Rate: 75 BPM',
                    scopeWaveform: 'https://i.imgur.com/6XyqL5p.png', // Similar to ECG
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'This method uses a strain gauge placed over a superficial artery (e.g., radial artery). The mechanical expansion and contraction of the artery with each heartbeat causes the strain gauge to deform, changing its resistance.',
                    procedureText: '1. Connect the strain gauge sensor to IN4 and IN5. \n2. Locate the radial artery on the wrist. \n3. Secure the sensor over the artery to detect the pulse.',
                    filterCutoff: { low: 0.05, high: 40 }
                },
                'Body Impedance': {
                    requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                    lcdOutput: 'Impedance: 500 Ω',
                    scopeWaveform: 'https://i.imgur.com/d2fG8hH.png', // Similar to respiration
                    scopeWaveformDual: null,
                    scopeChannels: 1,
                    principleText: 'Bioelectrical Impedance Analysis (BIA) measures the opposition to the flow of a small, high-frequency alternating current through the body. Changes in impedance can be correlated with changes in fluid volume, such as those caused by respiration or the cardiac cycle.',
                    procedureText: '1. Connect four electrodes to the subject (e.g., on hand and foot). \n2. Two electrodes (IN1, IN4) inject the current, two (IN2, IN3) measure the voltage drop. \n3. Observe impedance changes during breathing.',
                    filterCutoff: { low: 0.1, high: 30 }
                }
            };

            // --- DOM ELEMENT REFERENCES ---
            const moduleSelect = document.getElementById('module-select');
            const sensorList = document.getElementById('sensor-list');
            const freqSlider = document.getElementById('freq-slider');
            const ampSlider = document.getElementById('amp-slider');
            const freqValue = document.getElementById('freq-value');
            const ampValue = document.getElementById('amp-value');
            const analysisModeBtn = document.getElementById('analysis-mode-btn');
            const safetyWarning = document.getElementById('safety-warning');
            const virtualLcd = document.getElementById('virtual-lcd');
            const scopeImg = document.getElementById('scope-img');
            const scopeCanvas = document.getElementById('scope-canvas');
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            const dataOutputTab = document.getElementById('tab-data-output');
            const dataModal = document.getElementById('data-modal');
            const modalCloseBtn = document.getElementById('modal-close-btn');

            let isAnalysisMode = false;
            let animationFrameId;

            // --- INITIALIZATION ---
            function initialize() {
                // Populate module dropdown
                Object.keys(labModules).forEach(key => {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = key;
                    moduleSelect.appendChild(option);
                });

                // Populate sensor list
                for (let i = 1; i <= 7; i++) {
                    const li = document.createElement('li');
                    li.className = 'sensor-item';
                    li.innerHTML = `
                        <input type="checkbox" id="sensor-in${i}" data-sensor="IN${i}">
                        <label for="sensor-in${i}">IN${i}</label>
                        <span class="led" id="led-in${i}"></span>
                    `;
                    sensorList.appendChild(li);
                }
                
                // Set up event listeners
                moduleSelect.addEventListener('change', updateVirtualLab);
                freqSlider.addEventListener('input', updateFuncGenDisplay);
                ampSlider.addEventListener('input', updateFuncGenDisplay);
                analysisModeBtn.addEventListener('click', toggleAnalysisMode);
                modalCloseBtn.addEventListener('click', () => dataModal.classList.add('hidden'));
                
                document.querySelectorAll('.sensor-item input').forEach(checkbox => {
                    checkbox.addEventListener('change', checkSensorLEDs);
                });

                tabButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        tabContents.forEach(content => content.classList.add('hidden'));
                        document.getElementById(`tab-${button.dataset.tab}`).classList.remove('hidden');
                    });
                });

                // Initial load
                updateVirtualLab();
            }

            // --- CORE UPDATE FUNCTION ---
            function updateVirtualLab() {
                if (isAnalysisMode) {
                    toggleAnalysisMode(); // Exit analysis mode if module changes
                }
                const selectedModuleKey = moduleSelect.value;
                const moduleData = labModules[selectedModuleKey];

                updateLCD(selectedModuleKey, moduleData);
                updateScope(moduleData);
                updateTabs(selectedModuleKey, moduleData);
                updateSafetyWarning(selectedModuleKey);
                checkSensorLEDs();
                analysisModeBtn.disabled = false;
            }

            // --- HELPER UI UPDATE FUNCTIONS ---
            function updateLCD(moduleName, moduleData) {
                const freq = freqSlider.value;
                const amp = parseFloat(ampSlider.value).toFixed(1);
                virtualLcd.innerHTML = `
                    <span>INPUT: ${moduleName}</span>
                    <span>FUNC GEN: ${freq}Hz ${amp}Vpp</span>
                    <span>OUTPUT: ${moduleData.lcdOutput}</span>
                `.trim();
            }
            
            function updateFuncGenDisplay() {
                freqValue.textContent = `${freqSlider.value} Hz`;
                ampValue.textContent = `${parseFloat(ampSlider.value).toFixed(1)} Vpp`;
                updateLCD(moduleSelect.value, labModules[moduleSelect.value]);
            }

            function updateScope(moduleData) {
                const imgUrl = (moduleData.scopeChannels === 2 && moduleData.scopeWaveformDual) 
                    ? moduleData.scopeWaveformDual 
                    : moduleData.scopeWaveform;
                scopeImg.src = imgUrl;
            }

            function updateTabs(moduleKey, moduleData) {
                document.getElementById('tab-principle').innerHTML = `<h3>Principle</h3><p>${moduleData.principleText}</p>`;
                document.getElementById('tab-procedure').innerHTML = `<h3>Procedure</h3><p style="white-space: pre-wrap;">${moduleData.procedureText}</p>`;
                
                dataOutputTab.innerHTML = `
                    <h3>Data Output Simulation</h3>
                    <p>Analog Outputs Active: ${moduleData.scopeChannels}</p>
                    <p>Digital Interface: RS232 @ 9600 Baud</p>
                    <button id="download-btn">Simulate RS232 Data Download</button>
                `;
                document.getElementById('download-btn').addEventListener('click', () => showDataModal(moduleKey));
            }

            function updateSafetyWarning(moduleKey) {
                safetyWarning.classList.toggle('hidden', moduleKey !== 'Body Impedance');
            }

            function checkSensorLEDs() {
                const selectedModuleKey = moduleSelect.value;
                const required = labModules[selectedModuleKey].requiredSensors;
                
                for (let i = 1; i <= 7; i++) {
                    const checkbox = document.getElementById(`sensor-in${i}`);
                    const led = document.getElementById(`led-in${i}`);
                    const sensorId = `IN${i}`;
                    
                    const isRequired = required.includes(sensorId);
                    const isConnected = checkbox.checked;
                    
                    led.classList.toggle('led-on', isRequired && isConnected);
                }
            }
            
            function showDataModal(moduleKey) {
                const table = document.getElementById('data-table');
                let tableHTML = `<thead><tr><th>Time (s)</th><th>Voltage (V)</th></tr></thead><tbody>`;
                for(let i = 0; i < 10; i++) {
                    const time = (i * 0.01).toFixed(2);
                    const voltage = (Math.random() * 2 - 1).toFixed(4); // Random sample data
                    tableHTML += `<tr><td>${time}</td><td>${voltage}</td></tr>`;
                }
                tableHTML += `</tbody>`;
                table.innerHTML = tableHTML;
                dataModal.classList.remove('hidden');
            }

            // --- CIRCUIT ANALYSIS MODE ---
            function toggleAnalysisMode() {
                isAnalysisMode = !isAnalysisMode;
                analysisModeBtn.textContent = isAnalysisMode ? 'Exit Circuit Analysis Mode' : 'Enter Circuit Analysis Mode';
                
                scopeImg.classList.toggle('hidden', isAnalysisMode);
                scopeCanvas.classList.toggle('hidden', !isAnalysisMode);
                
                if (isAnalysisMode) {
                    // Resize canvas to fit container
                    const rect = scopeCanvas.parentElement.getBoundingClientRect();
                    scopeCanvas.width = rect.width;
                    scopeCanvas.height = rect.height;
                    animationFrameId = requestAnimationFrame(drawWaves);
                } else {
                    cancelAnimationFrame(animationFrameId);
                }
            }

            function drawWaves() {
                if (!isAnalysisMode) return;

                const ctx = scopeCanvas.getContext('2d');
                const width = scopeCanvas.width;
                const height = scopeCanvas.height;
                const midY = height / 2;

                const freq = parseFloat(freqSlider.value);
                const inputAmp = parseFloat(ampSlider.value);
                
                const moduleData = labModules[moduleSelect.value];
                const lowCutoff = moduleData.filterCutoff.low;
                const highCutoff = moduleData.filterCutoff.high;

                // Simple first-order RC filter simulation
                const highPassGain = 1 / Math.sqrt(1 + Math.pow(lowCutoff / freq, 2));
                const lowPassGain = 1 / Math.sqrt(1 + Math.pow(freq / highCutoff, 2));
                const totalGain = highPassGain * lowPassGain;
                const outputAmp = inputAmp * totalGain;

                ctx.clearRect(0, 0, width, height);

                // Draw Input Wave (Blue)
                ctx.beginPath();
                ctx.strokeStyle = '#3498db'; // Blue
                ctx.lineWidth = 2;
                for (let x = 0; x < width; x++) {
                    const angle = (x / width) * Math.PI * 2 * (freq / 50); // Normalize frequency for display
                    const y = midY - (Math.sin(angle) * (inputAmp / 10) * (midY * 0.8));
                    if (x === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                // Draw Output Wave (Yellow)
                ctx.beginPath();
                ctx.strokeStyle = '#f1c40f'; // Yellow
                ctx.lineWidth = 2;
                for (let x = 0; x < width; x++) {
                    const angle = (x / width) * Math.PI * 2 * (freq / 50);
                    const y = midY - (Math.sin(angle) * (outputAmp / 10) * (midY * 0.8));
                    if (x === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                animationFrameId = requestAnimationFrame(drawWaves);
            }

            // --- START THE APP ---
            initialize();
        });
    </script>

</body>
</html>
