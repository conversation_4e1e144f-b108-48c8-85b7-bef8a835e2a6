<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v3.0: Advanced Virtual Lab with Circuit Design Toolkit</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-dark: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --panel-bg: #252b3d;
            --border-color: #3a4556;
            --text-primary: #e2e8f0;
            --text-secondary: #a0aec0;
            --text-muted: #64748b;
            --accent-cyan: #00d4ff;
            --accent-green: #00ff88;
            --accent-yellow: #ffeb3b;
            --accent-red: #ff4757;
            --accent-purple: #8b5cf6;
            --accent-orange: #ff6b35;
            --button-bg: #2563eb;
            --button-hover: #1d4ed8;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --grid-color: #2a3441;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(90deg, var(--panel-bg) 0%, var(--bg-secondary) 100%);
            padding: 1rem 2rem;
            border-bottom: 2px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1600px;
            margin: 0 auto;
        }

        .header-left h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-left p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .header-right {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Mode Switcher */
        .mode-switcher {
            background: var(--bg-secondary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .mode-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .mode-btn {
            flex: 1;
            padding: 1rem 2rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--panel-bg);
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .mode-btn:hover {
            border-color: var(--accent-cyan);
            background: var(--bg-secondary);
            transform: translateY(-2px);
        }

        .mode-btn.active {
            border-color: var(--accent-cyan);
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            color: var(--bg-dark);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .mode-icon {
            font-size: 1.2rem;
        }

        /* Main Container */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Simulation Lab View */
        .simulation-lab {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            height: calc(100vh - 200px);
        }

        .control-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow-y: auto;
        }

        .display-area {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        /* Circuit Design Toolkit View */
        .circuit-toolkit {
            display: none;
            flex-direction: column;
            height: calc(100vh - 200px);
            gap: 1rem;
        }

        .toolkit-toolbar {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .toolkit-main {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 1rem;
            flex: 1;
            min-height: 0;
        }

        .canvas-container {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        .circuit-canvas {
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(var(--grid-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
            background-size: 20px 20px;
            border-radius: 8px;
            cursor: crosshair;
        }

        .analysis-panel {
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Buttons and Controls */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--button-bg), var(--accent-cyan));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: var(--bg-dark);
        }

        .btn-danger {
            background: var(--error);
            color: white;
        }

        /* Dropdowns */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            min-width: 200px;
            max-height: 300px;
            overflow-y: auto;
        }

        .dropdown.active .dropdown-content {
            display: block;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.2s ease;
        }

        .dropdown-item:hover {
            background: var(--bg-secondary);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        /* Component Library */
        .component-category {
            font-weight: 600;
            color: var(--accent-cyan);
            background: var(--bg-secondary);
        }

        .component-item {
            padding-left: 1.5rem;
            font-size: 0.85rem;
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .simulation-lab {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .toolkit-main {
                grid-template-columns: 1fr;
            }
            
            .analysis-panel {
                order: -1;
                max-height: 300px;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .mode-buttons {
                flex-direction: column;
            }
            
            .main-container {
                padding: 1rem;
            }
            
            .toolkit-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <h1>Bio-Signal Explorer v3.0</h1>
                <p>Advanced Virtual Lab with Circuit Design Toolkit</p>
            </div>
            <div class="header-right">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">
                    🏠 Home
                </button>
                <button type="button" class="btn btn-secondary" onclick="showHelp()">
                    ❓ Help
                </button>
            </div>
        </div>
    </header>

    <!-- Mode Switcher -->
    <div class="mode-switcher">
        <div class="mode-buttons">
            <button type="button" class="mode-btn active" id="simModeBtn" onclick="switchMode('simulation')">
                <span class="mode-icon">🔬</span>
                Simulation Lab
            </button>
            <button type="button" class="mode-btn" id="designModeBtn" onclick="switchMode('design')">
                <span class="mode-icon">⚡</span>
                Circuit Design Toolkit
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Simulation Lab View -->
        <div class="simulation-lab" id="simulationView">
            <!-- Control Panel -->
            <div class="control-panel">
                <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">🎛️ Control Hub</h2>
                
                <!-- Power Control -->
                <div style="margin-bottom: 1.5rem;">
                    <button type="button" class="btn btn-success" id="powerBtn" onclick="togglePower()" style="width: 100%;">
                        ⚡ Power On
                    </button>
                </div>

                <!-- Module Selection -->
                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Measurement Module:</label>
                    <div style="display: flex; gap: 0.5rem;">
                        <select id="moduleSelect" style="flex: 1; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); color: var(--text-primary);" onchange="selectModule()">
                            <option value="">Select Module...</option>
                            <option value="ecg">ECG - Electrocardiogram</option>
                            <option value="emg">EMG - Electromyography</option>
                            <option value="eeg">EEG - Electroencephalography</option>
                            <option value="eog">EOG - Electrooculography</option>
                            <option value="bp">Blood Pressure</option>
                            <option value="impedance">Body Impedance</option>
                        </select>
                        <button type="button" class="btn btn-secondary" id="viewCircuitBtn" onclick="viewInternalCircuit()" title="View Internal Circuit" disabled>
                            🔍
                        </button>
                    </div>
                </div>

                <!-- Sensor Connections -->
                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Sensor Connections:</label>
                    <div id="sensorGrid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem;">
                        <!-- Sensors will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Function Generator -->
                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Function Generator:</label>
                    <div style="margin-bottom: 0.75rem;">
                        <label style="font-size: 0.85rem; color: var(--text-secondary);">Frequency: <span id="freqValue">100</span> Hz</label>
                        <input type="range" id="freqSlider" min="1" max="1000" value="100" style="width: 100%; margin-top: 0.25rem;" oninput="updateFrequency()">
                    </div>
                    <div>
                        <label style="font-size: 0.85rem; color: var(--text-secondary);">Amplitude: <span id="ampValue">1.0</span> Vpp</label>
                        <input type="range" id="ampSlider" min="0.1" max="10" step="0.1" value="1.0" style="width: 100%; margin-top: 0.25rem;" oninput="updateAmplitude()">
                    </div>
                </div>

                <!-- Analysis Mode -->
                <div>
                    <button type="button" class="btn btn-warning" id="analysisBtn" onclick="toggleAnalysisMode()" style="width: 100%;" disabled>
                        📊 Enter Circuit Analysis
                    </button>
                </div>
            </div>

            <!-- Display Area -->
            <div class="display-area">
                <h2 style="margin-bottom: 1rem; color: var(--accent-cyan);">📺 System Display</h2>
                
                <!-- Status Panel -->
                <div id="statusPanel" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; text-align: center; color: var(--warning);">
                    System is powered off. Click Power On to begin.
                </div>

                <!-- Virtual LCD -->
                <div id="virtualLCD" style="background: #000; color: var(--accent-green); font-family: 'Roboto Mono', monospace; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; min-height: 80px; border: 2px solid var(--border-color);">
                    > SYSTEM OFFLINE<br>
                    > POWER ON TO START
                </div>

                <!-- Oscilloscope -->
                <div style="flex: 1; background: #000; border: 2px solid var(--border-color); border-radius: 8px; position: relative; margin-bottom: 1rem;">
                    <canvas id="oscilloscope" style="width: 100%; height: 100%; border-radius: 6px;"></canvas>
                </div>

                <!-- Tabs -->
                <div style="display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem;">
                    <button type="button" class="tab-btn active" onclick="showTab('info')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                        📋 Module Info
                    </button>
                    <button type="button" class="tab-btn" onclick="showTab('procedure')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                        📝 Procedure
                    </button>
                    <button type="button" class="tab-btn" onclick="showTab('export')" style="flex: 1; padding: 0.75rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent;">
                        💾 Export Data
                    </button>
                </div>

                <!-- Tab Content -->
                <div id="tabContent" style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; min-height: 120px;">
                    Select a measurement module to view information.
                </div>
            </div>
        </div>

        <!-- Circuit Design Toolkit View -->
        <div class="circuit-toolkit" id="designView">
            <!-- Toolbar -->
            <div class="toolkit-toolbar">
                <!-- Component Library -->
                <div class="dropdown" id="componentDropdown">
                    <button type="button" class="dropdown-btn" onclick="toggleDropdown('componentDropdown')">
                        🧩 Add Component
                        <span style="margin-left: auto;">▼</span>
                    </button>
                    <div class="dropdown-content">
                        <div class="dropdown-item component-category">Sources</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('function-generator')">📡 Function Generator</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('bio-signal')">💓 Bio-Signal Source</div>

                        <div class="dropdown-item component-category">Passive Components</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('resistor')">🔧 Resistor</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('capacitor')">⚡ Capacitor</div>

                        <div class="dropdown-item component-category">Active Components</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('op-amp')">🔺 Op-Amp</div>

                        <div class="dropdown-item component-category">Measurement</div>
                        <div class="dropdown-item component-item" onclick="selectComponent('oscilloscope')">📊 Oscilloscope Probe</div>
                    </div>
                </div>

                <!-- Template Library -->
                <div class="dropdown" id="templateDropdown">
                    <button type="button" class="dropdown-btn" onclick="toggleDropdown('templateDropdown')">
                        📋 Load Template
                        <span style="margin-left: auto;">▼</span>
                    </button>
                    <div class="dropdown-content">
                        <div class="dropdown-item" onclick="loadTemplate('rc-lowpass')">RC Low-Pass Filter</div>
                        <div class="dropdown-item" onclick="loadTemplate('rc-highpass')">RC High-Pass Filter</div>
                        <div class="dropdown-item" onclick="loadTemplate('non-inverting-amp')">Non-inverting Op-Amp</div>
                        <div class="dropdown-item" onclick="loadTemplate('bandpass-filter')">Band-Pass Filter</div>
                        <div class="dropdown-item" onclick="loadTemplate('ecg-frontend')">ECG Frontend Circuit</div>
                        <div class="dropdown-item" onclick="loadTemplate('emg-amplifier')">EMG Amplifier</div>
                    </div>
                </div>

                <!-- Simulation Controls -->
                <div style="display: flex; gap: 0.5rem;">
                    <button type="button" class="btn btn-success" id="runSimBtn" onclick="runCircuitSimulation()">
                        ▶️ Run Simulation
                    </button>
                    <button type="button" class="btn btn-danger" id="stopSimBtn" onclick="stopCircuitSimulation()" disabled>
                        ⏹️ Stop
                    </button>
                </div>

                <!-- Analysis Tools -->
                <div style="display: flex; gap: 0.5rem;">
                    <button type="button" class="btn btn-primary" onclick="generateBodePlot()">
                        📈 Bode Plot
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearCanvas()">
                        🗑️ Clear
                    </button>
                </div>
            </div>

            <!-- Main Toolkit Area -->
            <div class="toolkit-main">
                <!-- Canvas Container -->
                <div class="canvas-container">
                    <canvas id="circuitCanvas" class="circuit-canvas"></canvas>

                    <!-- Component Properties Panel (Hidden by default) -->
                    <div id="propertiesPanel" class="hidden" style="position: absolute; top: 20px; right: 20px; background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 8px; padding: 1rem; min-width: 200px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
                        <h4 style="margin-bottom: 0.5rem; color: var(--accent-cyan);">Component Properties</h4>
                        <div id="propertiesContent">
                            <!-- Properties will be populated by JavaScript -->
                        </div>
                        <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                            <button type="button" class="btn btn-primary" onclick="applyProperties()" style="flex: 1; font-size: 0.8rem;">Apply</button>
                            <button type="button" class="btn btn-secondary" onclick="closeProperties()" style="flex: 1; font-size: 0.8rem;">Close</button>
                        </div>
                    </div>
                </div>

                <!-- Analysis Panel -->
                <div class="analysis-panel">
                    <h3 style="margin-bottom: 1rem; color: var(--accent-cyan);">📊 Analysis</h3>

                    <!-- Analysis Tabs -->
                    <div style="display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 1rem;">
                        <button type="button" class="analysis-tab active" onclick="showAnalysisTab('oscilloscope')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                            📺 Scope
                        </button>
                        <button type="button" class="analysis-tab" onclick="showAnalysisTab('bode')" style="flex: 1; padding: 0.5rem; border: none; background: transparent; color: var(--text-secondary); cursor: pointer; border-bottom: 2px solid transparent; font-size: 0.85rem;">
                            📈 Bode
                        </button>
                    </div>

                    <!-- Analysis Content -->
                    <div id="analysisContent" style="flex: 1; background: #000; border: 1px solid var(--border-color); border-radius: 8px; position: relative; min-height: 200px;">
                        <canvas id="analysisCanvas" style="width: 100%; height: 100%; border-radius: 7px;"></canvas>
                        <div id="analysisPlaceholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-muted); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                            <div>Run simulation to see results</div>
                        </div>
                    </div>

                    <!-- Analysis Info -->
                    <div id="analysisInfo" style="margin-top: 1rem; padding: 0.75rem; background: var(--bg-secondary); border-radius: 8px; font-size: 0.85rem; color: var(--text-secondary);">
                        <div><strong>Status:</strong> <span id="simStatus">Ready</span></div>
                        <div><strong>Components:</strong> <span id="componentCount">0</span></div>
                        <div><strong>Connections:</strong> <span id="connectionCount">0</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
        <div style="background: var(--panel-bg); border: 1px solid var(--border-color); border-radius: 16px; padding: 2rem; max-width: 600px; max-height: 80vh; overflow-y: auto; margin: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h2 style="color: var(--accent-cyan);">🎓 Help Guide</h2>
                <button type="button" onclick="hideHelp()" style="background: none; border: none; color: var(--text-secondary); font-size: 1.5rem; cursor: pointer;">×</button>
            </div>

            <div style="space-y: 1rem;">
                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔬 Simulation Lab</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Use the simulation lab to experiment with pre-built biomedical measurement modules. Power on the system, select a module, connect sensors, and observe real-time signals.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">⚡ Circuit Design Toolkit</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Build and analyze circuits from scratch. Add components, connect them with wires, and run simulations to see how they behave. Use templates to get started quickly.</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">🔍 View Internal Circuits</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Click the magnifying glass button next to module selection to see the internal circuit design of any measurement module.</p>
                </div>

                <div>
                    <h3 style="color: var(--accent-green); margin-bottom: 0.5rem;">📊 Analysis Tools</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Use the oscilloscope to view time-domain signals and generate Bode plots to analyze frequency response characteristics.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global State Management
        const AppState = {
            currentMode: 'simulation',
            powerOn: false,
            selectedModule: null,
            analysisMode: false,
            circuitSimulation: {
                running: false,
                components: [],
                connections: [],
                selectedComponent: null,
                draggedComponent: null
            },
            animationFrameId: null
        };

        // Module Definitions
        const LabModules = {
            ecg: {
                name: "ECG - Electrocardiogram",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN5'],
                description: "Measures electrical activity of the heart through electrodes placed on the skin.",
                procedure: "1. Clean skin areas\n2. Attach electrodes to RA, LA, LL, RL\n3. Ask subject to relax\n4. Record P-QRS-T waveform",
                circuitTemplate: 'ecg-frontend',
                frequency: 1.2,
                amplitude: 1.0
            },
            emg: {
                name: "EMG - Electromyography",
                requiredSensors: ['IN1', 'IN2', 'IN5'],
                description: "Records electrical potential generated by muscle cells during activation.",
                procedure: "1. Place electrodes over target muscle\n2. Place ground electrode on bony prominence\n3. Record during muscle contractions",
                circuitTemplate: 'emg-amplifier',
                frequency: 150,
                amplitude: 0.5
            },
            eeg: {
                name: "EEG - Electroencephalography",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: "Measures electrical activity of the brain from the scalp.",
                procedure: "1. Place electrodes according to 10-20 system\n2. Ensure impedance < 5kΩ\n3. Record with eyes open/closed",
                circuitTemplate: 'bandpass-filter',
                frequency: 10,
                amplitude: 0.1
            },
            eog: {
                name: "EOG - Electrooculography",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4', 'IN5'],
                description: "Measures eye movements based on corneal-retinal potential difference.",
                procedure: "1. Place electrodes around eyes\n2. Place ground on forehead\n3. Ask subject to follow target",
                circuitTemplate: 'rc-highpass',
                frequency: 0.5,
                amplitude: 0.8
            },
            bp: {
                name: "Blood Pressure Monitor",
                requiredSensors: ['IN6'],
                description: "Oscillometric blood pressure measurement using pressure cuff.",
                procedure: "1. Wrap cuff around upper arm\n2. Inflate above systolic pressure\n3. Slowly deflate and record oscillations",
                circuitTemplate: 'rc-lowpass',
                frequency: 1.0,
                amplitude: 2.0
            },
            impedance: {
                name: "Body Impedance Analyzer",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                description: "Measures body composition using bioelectrical impedance analysis.",
                procedure: "1. Connect four electrodes\n2. Apply safe AC current\n3. Measure voltage drop\n4. Calculate impedance",
                circuitTemplate: 'non-inverting-amp',
                frequency: 1000,
                amplitude: 0.001
            }
        };

        // Circuit Templates
        const CircuitTemplates = {
            'rc-lowpass': {
                name: 'RC Low-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 1000, amplitude: 1 } },
                    { type: 'resistor', x: 250, y: 150, id: 'r1', properties: { resistance: 1000 } },
                    { type: 'capacitor', x: 400, y: 150, id: 'c1', properties: { capacitance: 0.1 } },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'fg1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'scope1-in' }
                ]
            },
            'rc-highpass': {
                name: 'RC High-Pass Filter',
                components: [
                    { type: 'function-generator', x: 100, y: 150, id: 'fg1', properties: { frequency: 100, amplitude: 1 } },
                    { type: 'capacitor', x: 250, y: 150, id: 'c1', properties: { capacitance: 1 } },
                    { type: 'resistor', x: 400, y: 150, id: 'r1', properties: { resistance: 1000 } },
                    { type: 'oscilloscope', x: 550, y: 150, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'non-inverting-amp': {
                name: 'Non-inverting Op-Amp Amplifier',
                components: [
                    { type: 'function-generator', x: 100, y: 200, id: 'fg1', properties: { frequency: 1000, amplitude: 0.1 } },
                    { type: 'op-amp', x: 300, y: 200, id: 'op1', properties: { gain: 10 } },
                    { type: 'resistor', x: 250, y: 250, id: 'r1', properties: { resistance: 1000 } },
                    { type: 'resistor', x: 350, y: 250, id: 'r2', properties: { resistance: 9000 } },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'fg1-out', to: 'op1-in+' },
                    { from: 'op1-in-', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'op1-out' },
                    { from: 'op1-out', to: 'scope1-in' }
                ]
            },
            'bandpass-filter': {
                name: 'Band-Pass Filter (HPF + LPF)',
                components: [
                    { type: 'function-generator', x: 80, y: 200, id: 'fg1', properties: { frequency: 100, amplitude: 1 } },
                    { type: 'capacitor', x: 200, y: 200, id: 'c1', properties: { capacitance: 1 } },
                    { type: 'resistor', x: 320, y: 200, id: 'r1', properties: { resistance: 1000 } },
                    { type: 'resistor', x: 440, y: 200, id: 'r2', properties: { resistance: 1000 } },
                    { type: 'capacitor', x: 560, y: 200, id: 'c2', properties: { capacitance: 0.1 } },
                    { type: 'oscilloscope', x: 680, y: 200, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'fg1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'r2-in' },
                    { from: 'r2-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            },
            'ecg-frontend': {
                name: 'ECG Frontend Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'ecg1', properties: { signalType: 'ECG', amplitude: 0.001 } },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 1000 } },
                    { type: 'capacitor', x: 360, y: 200, id: 'c1', properties: { capacitance: 0.1 } },
                    { type: 'resistor', x: 480, y: 200, id: 'r1', properties: { resistance: 10000 } },
                    { type: 'oscilloscope', x: 620, y: 200, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'ecg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'r1-in' },
                    { from: 'r1-out', to: 'scope1-in' }
                ]
            },
            'emg-amplifier': {
                name: 'EMG Amplifier Circuit',
                components: [
                    { type: 'bio-signal', x: 80, y: 200, id: 'emg1', properties: { signalType: 'EMG', amplitude: 0.0001 } },
                    { type: 'op-amp', x: 220, y: 200, id: 'op1', properties: { gain: 10000 } },
                    { type: 'capacitor', x: 360, y: 180, id: 'c1', properties: { capacitance: 0.01 } },
                    { type: 'capacitor', x: 360, y: 220, id: 'c2', properties: { capacitance: 10 } },
                    { type: 'oscilloscope', x: 500, y: 200, id: 'scope1', properties: {} }
                ],
                connections: [
                    { from: 'emg1-out', to: 'op1-in+' },
                    { from: 'op1-out', to: 'c1-in' },
                    { from: 'c1-out', to: 'c2-in' },
                    { from: 'c2-out', to: 'scope1-in' }
                ]
            }
        };

        // Mode Management
        function switchMode(mode) {
            AppState.currentMode = mode;

            // Update button states
            document.getElementById('simModeBtn').classList.toggle('active', mode === 'simulation');
            document.getElementById('designModeBtn').classList.toggle('active', mode === 'design');

            // Show/hide views
            document.getElementById('simulationView').style.display = mode === 'simulation' ? 'grid' : 'none';
            document.getElementById('designView').style.display = mode === 'design' ? 'flex' : 'none';

            // Initialize canvas if switching to design mode
            if (mode === 'design') {
                initializeCircuitCanvas();
            }
        }

        // Simulation Lab Functions
        function togglePower() {
            AppState.powerOn = !AppState.powerOn;
            const powerBtn = document.getElementById('powerBtn');
            const statusPanel = document.getElementById('statusPanel');
            const virtualLCD = document.getElementById('virtualLCD');

            if (AppState.powerOn) {
                powerBtn.textContent = '🔴 Power Off';
                powerBtn.className = 'btn btn-danger';
                statusPanel.textContent = 'System powered on. Select a measurement module.';
                statusPanel.style.color = 'var(--success)';
                virtualLCD.innerHTML = '> SYSTEM ONLINE<br>> SELECT MODULE TO BEGIN';

                // Enable controls
                document.getElementById('moduleSelect').disabled = false;
                document.getElementById('analysisBtn').disabled = false;
            } else {
                powerBtn.textContent = '⚡ Power On';
                powerBtn.className = 'btn btn-success';
                statusPanel.textContent = 'System is powered off. Click Power On to begin.';
                statusPanel.style.color = 'var(--warning)';
                virtualLCD.innerHTML = '> SYSTEM OFFLINE<br>> POWER ON TO START';

                // Disable controls
                document.getElementById('moduleSelect').disabled = true;
                document.getElementById('moduleSelect').value = '';
                document.getElementById('analysisBtn').disabled = true;
                document.getElementById('viewCircuitBtn').disabled = true;
                AppState.selectedModule = null;

                // Clear oscilloscope
                clearOscilloscope();
            }

            updateSensorGrid();
            updateTabContent();
        }

        function selectModule() {
            const moduleSelect = document.getElementById('moduleSelect');
            const selectedValue = moduleSelect.value;

            if (selectedValue && LabModules[selectedValue]) {
                AppState.selectedModule = selectedValue;
                document.getElementById('viewCircuitBtn').disabled = false;

                const module = LabModules[selectedValue];
                const virtualLCD = document.getElementById('virtualLCD');
                virtualLCD.innerHTML = `> MODULE: ${module.name}<br>> CONNECT SENSORS TO BEGIN`;

                updateSensorGrid();
                updateTabContent();
                startOscilloscopeAnimation();
            } else {
                AppState.selectedModule = null;
                document.getElementById('viewCircuitBtn').disabled = true;
                clearOscilloscope();
            }
        }

        function updateSensorGrid() {
            const sensorGrid = document.getElementById('sensorGrid');
            const sensors = ['IN1', 'IN2', 'IN3', 'IN4', 'IN5', 'IN6', 'IN7'];

            sensorGrid.innerHTML = '';

            sensors.forEach(sensor => {
                const sensorDiv = document.createElement('div');
                sensorDiv.style.cssText = 'display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: var(--bg-secondary); border-radius: 6px; border: 1px solid var(--border-color);';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = sensor;
                checkbox.disabled = !AppState.powerOn;

                const label = document.createElement('label');
                label.textContent = sensor;
                label.style.cssText = 'flex: 1; font-size: 0.85rem; cursor: pointer;';
                label.setAttribute('for', sensor);

                const led = document.createElement('div');
                led.style.cssText = 'width: 8px; height: 8px; border-radius: 50%; background: var(--border-color); transition: all 0.3s ease;';

                // Check if sensor is required for current module
                if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                    const requiredSensors = LabModules[AppState.selectedModule].requiredSensors;
                    if (requiredSensors.includes(sensor)) {
                        checkbox.addEventListener('change', function() {
                            if (this.checked) {
                                led.style.background = 'var(--accent-green)';
                                led.style.boxShadow = '0 0 8px var(--accent-green)';
                            } else {
                                led.style.background = 'var(--border-color)';
                                led.style.boxShadow = 'none';
                            }
                        });
                    }
                }

                sensorDiv.appendChild(checkbox);
                sensorDiv.appendChild(label);
                sensorDiv.appendChild(led);
                sensorGrid.appendChild(sensorDiv);
            });
        }

        function updateFrequency() {
            const freqSlider = document.getElementById('freqSlider');
            const freqValue = document.getElementById('freqValue');
            freqValue.textContent = freqSlider.value;
        }

        function updateAmplitude() {
            const ampSlider = document.getElementById('ampSlider');
            const ampValue = document.getElementById('ampValue');
            ampValue.textContent = parseFloat(ampSlider.value).toFixed(1);
        }

        function toggleAnalysisMode() {
            AppState.analysisMode = !AppState.analysisMode;
            const analysisBtn = document.getElementById('analysisBtn');

            if (AppState.analysisMode) {
                analysisBtn.textContent = '📊 Exit Analysis Mode';
                analysisBtn.className = 'btn btn-secondary';
            } else {
                analysisBtn.textContent = '📊 Enter Circuit Analysis';
                analysisBtn.className = 'btn btn-warning';
            }
        }

        function viewInternalCircuit() {
            if (AppState.selectedModule && LabModules[AppState.selectedModule]) {
                const module = LabModules[AppState.selectedModule];
                switchMode('design');
                loadTemplate(module.circuitTemplate);
            }
        }

        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';

            updateTabContent(tabName);
        }

        function updateTabContent(activeTab = 'info') {
            const tabContent = document.getElementById('tabContent');

            if (!AppState.selectedModule) {
                tabContent.innerHTML = 'Select a measurement module to view information.';
                return;
            }

            const module = LabModules[AppState.selectedModule];

            switch (activeTab) {
                case 'info':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">${module.name}</h4>
                        <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 1rem;">${module.description}</p>
                        <div style="background: var(--panel-bg); padding: 0.75rem; border-radius: 6px; border-left: 3px solid var(--accent-green);">
                            <strong>Required Sensors:</strong> ${module.requiredSensors.join(', ')}
                        </div>
                    `;
                    break;
                case 'procedure':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">Measurement Procedure</h4>
                        <pre style="color: var(--text-secondary); line-height: 1.6; white-space: pre-wrap; font-family: inherit;">${module.procedure}</pre>
                    `;
                    break;
                case 'export':
                    tabContent.innerHTML = `
                        <h4 style="color: var(--accent-cyan); margin-bottom: 0.5rem;">Data Export</h4>
                        <p style="color: var(--text-secondary); margin-bottom: 1rem;">Export measurement data for offline analysis:</p>
                        <button type="button" class="btn btn-primary" onclick="exportData()">📥 Download Sample Data (.csv)</button>
                    `;
                    break;
            }
        }

        // Oscilloscope Functions
        function startOscilloscopeAnimation() {
            if (AppState.animationFrameId) {
                cancelAnimationFrame(AppState.animationFrameId);
            }

            if (AppState.powerOn && AppState.selectedModule) {
                drawOscilloscope();
            }
        }

        function drawOscilloscope() {
            const canvas = document.getElementById('oscilloscope');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw grid
            drawGrid(ctx, canvas.width, canvas.height);

            if (AppState.selectedModule) {
                const module = LabModules[AppState.selectedModule];
                const time = Date.now() * 0.001;

                if (AppState.analysisMode) {
                    // Draw circuit analysis waveforms
                    drawAnalysisWaveforms(ctx, canvas.width, canvas.height, time);
                } else {
                    // Draw physiological signal
                    drawPhysiologicalSignal(ctx, canvas.width, canvas.height, time, module);
                }
            }

            AppState.animationFrameId = requestAnimationFrame(drawOscilloscope);
        }

        function drawGrid(ctx, width, height) {
            ctx.strokeStyle = '#1a2332';
            ctx.lineWidth = 1;

            // Vertical lines
            for (let x = 0; x <= width; x += 40) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Horizontal lines
            for (let y = 0; y <= height; y += 30) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        }

        function drawPhysiologicalSignal(ctx, width, height, time, module) {
            const centerY = height / 2;
            const scale = 60;

            ctx.strokeStyle = 'var(--accent-green)';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                let y = centerY;

                switch (module.name.split(' ')[0]) {
                    case 'ECG':
                        y = centerY - scale * generateECGWave(t);
                        break;
                    case 'EMG':
                        y = centerY - scale * generateEMGWave(t);
                        break;
                    case 'EEG':
                        y = centerY - scale * generateEEGWave(t);
                        break;
                    default:
                        y = centerY - scale * Math.sin(2 * Math.PI * module.frequency * t * 0.1) * module.amplitude;
                }

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        function drawAnalysisWaveforms(ctx, width, height, time) {
            const centerY = height / 2;
            const freq = parseFloat(document.getElementById('freqSlider').value);
            const amp = parseFloat(document.getElementById('ampSlider').value);

            // Input signal (blue)
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY - amp * 30 * Math.sin(2 * Math.PI * freq * t * 0.01);

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // Output signal (yellow) - simulated filter response
            const gain = calculateFilterGain(freq);
            ctx.strokeStyle = '#fbbf24';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY - amp * gain * 30 * Math.sin(2 * Math.PI * freq * t * 0.01);

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        function generateECGWave(t) {
            const heartRate = 1.2;
            const phase = (t * heartRate) % 1;

            if (phase < 0.1) return 0.1 * Math.sin(phase * 10 * Math.PI);
            if (phase < 0.2) return -0.3 * Math.sin((phase - 0.1) * 10 * Math.PI);
            if (phase < 0.3) return 1.0 * Math.sin((phase - 0.2) * 10 * Math.PI);
            if (phase < 0.4) return 0.3 * Math.sin((phase - 0.3) * 10 * Math.PI);
            return 0.05 * Math.sin((phase - 0.4) * 5 * Math.PI);
        }

        function generateEMGWave(t) {
            return (Math.random() - 0.5) * 0.5 * (1 + Math.sin(t * 2));
        }

        function generateEEGWave(t) {
            return 0.1 * (Math.sin(t * 8) + 0.5 * Math.sin(t * 12) + 0.3 * Math.sin(t * 20)) + (Math.random() - 0.5) * 0.05;
        }

        function calculateFilterGain(frequency) {
            // Simple low-pass filter simulation
            const cutoffFreq = 100;
            return 1 / Math.sqrt(1 + Math.pow(frequency / cutoffFreq, 2));
        }

        function clearOscilloscope() {
            if (AppState.animationFrameId) {
                cancelAnimationFrame(AppState.animationFrameId);
                AppState.animationFrameId = null;
            }

            const canvas = document.getElementById('oscilloscope');
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            drawGrid(ctx, canvas.width, canvas.height);
        }

        // Circuit Design Toolkit Functions
        function initializeCircuitCanvas() {
            const canvas = document.getElementById('circuitCanvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add event listeners
            canvas.addEventListener('click', handleCanvasClick);
            canvas.addEventListener('mousedown', handleCanvasMouseDown);
            canvas.addEventListener('mousemove', handleCanvasMouseMove);
            canvas.addEventListener('mouseup', handleCanvasMouseUp);

            updateCircuitInfo();
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const allDropdowns = document.querySelectorAll('.dropdown');

            // Close other dropdowns
            allDropdowns.forEach(d => {
                if (d.id !== dropdownId) {
                    d.classList.remove('active');
                }
            });

            dropdown.classList.toggle('active');
        }

        function selectComponent(componentType) {
            AppState.circuitSimulation.selectedComponent = componentType;
            document.getElementById('componentDropdown').classList.remove('active');

            // Change cursor to indicate component selection
            const canvas = document.getElementById('circuitCanvas');
            canvas.style.cursor = 'crosshair';
        }

        function loadTemplate(templateName) {
            if (CircuitTemplates[templateName]) {
                const template = CircuitTemplates[templateName];
                AppState.circuitSimulation.components = [...template.components];
                AppState.circuitSimulation.connections = [...template.connections];

                drawCircuitCanvas();
                updateCircuitInfo();

                document.getElementById('templateDropdown').classList.remove('active');
            }
        }

        function handleCanvasClick(event) {
            const canvas = event.target;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            if (AppState.circuitSimulation.selectedComponent) {
                // Place component
                const componentId = `${AppState.circuitSimulation.selectedComponent}_${Date.now()}`;
                const component = {
                    type: AppState.circuitSimulation.selectedComponent,
                    x: Math.round(x / 20) * 20, // Snap to grid
                    y: Math.round(y / 20) * 20,
                    id: componentId,
                    properties: getDefaultProperties(AppState.circuitSimulation.selectedComponent)
                };

                AppState.circuitSimulation.components.push(component);
                AppState.circuitSimulation.selectedComponent = null;
                canvas.style.cursor = 'default';

                drawCircuitCanvas();
                updateCircuitInfo();
            } else {
                // Check if clicking on existing component
                const clickedComponent = findComponentAt(x, y);
                if (clickedComponent) {
                    showComponentProperties(clickedComponent);
                }
            }
        }

        function handleCanvasMouseDown(event) {
            // Handle component dragging
            const canvas = event.target;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            const component = findComponentAt(x, y);
            if (component) {
                AppState.circuitSimulation.draggedComponent = component;
                canvas.style.cursor = 'grabbing';
            }
        }

        function handleCanvasMouseMove(event) {
            if (AppState.circuitSimulation.draggedComponent) {
                const canvas = event.target;
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                AppState.circuitSimulation.draggedComponent.x = Math.round(x / 20) * 20;
                AppState.circuitSimulation.draggedComponent.y = Math.round(y / 20) * 20;

                drawCircuitCanvas();
            }
        }

        function handleCanvasMouseUp(event) {
            if (AppState.circuitSimulation.draggedComponent) {
                AppState.circuitSimulation.draggedComponent = null;
                event.target.style.cursor = 'default';
            }
        }

        function findComponentAt(x, y) {
            return AppState.circuitSimulation.components.find(comp => {
                return x >= comp.x - 25 && x <= comp.x + 25 &&
                       y >= comp.y - 25 && y <= comp.y + 25;
            });
        }

        function getDefaultProperties(componentType) {
            switch (componentType) {
                case 'resistor':
                    return { resistance: 1000 };
                case 'capacitor':
                    return { capacitance: 1 };
                case 'op-amp':
                    return { gain: 10 };
                case 'function-generator':
                    return { frequency: 1000, amplitude: 1 };
                case 'bio-signal':
                    return { signalType: 'ECG', amplitude: 0.001 };
                default:
                    return {};
            }
        }

        function drawCircuitCanvas() {
            const canvas = document.getElementById('circuitCanvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.fillStyle = 'var(--panel-bg)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw components
            AppState.circuitSimulation.components.forEach(component => {
                drawComponent(ctx, component);
            });

            // Draw connections
            AppState.circuitSimulation.connections.forEach(connection => {
                drawConnection(ctx, connection);
            });
        }

        function drawComponent(ctx, component) {
            const { x, y, type, id } = component;

            ctx.save();
            ctx.translate(x, y);

            // Set component style
            ctx.fillStyle = 'var(--text-primary)';
            ctx.strokeStyle = 'var(--accent-cyan)';
            ctx.lineWidth = 2;

            switch (type) {
                case 'resistor':
                    drawResistor(ctx);
                    break;
                case 'capacitor':
                    drawCapacitor(ctx);
                    break;
                case 'op-amp':
                    drawOpAmp(ctx);
                    break;
                case 'function-generator':
                    drawFunctionGenerator(ctx);
                    break;
                case 'bio-signal':
                    drawBioSignal(ctx);
                    break;
                case 'oscilloscope':
                    drawOscilloscope(ctx);
                    break;
            }

            // Draw component label
            ctx.fillStyle = 'var(--text-secondary)';
            ctx.font = '10px Inter';
            ctx.textAlign = 'center';
            ctx.fillText(id.split('_')[0], 0, 35);

            ctx.restore();
        }

        function drawResistor(ctx) {
            ctx.beginPath();
            ctx.rect(-20, -8, 40, 16);
            ctx.stroke();

            // Zigzag pattern
            ctx.beginPath();
            ctx.moveTo(-15, 0);
            for (let i = -15; i <= 15; i += 5) {
                ctx.lineTo(i, (i % 10 === 0) ? -5 : 5);
            }
            ctx.stroke();
        }

        function drawCapacitor(ctx) {
            ctx.beginPath();
            ctx.moveTo(-5, -15);
            ctx.lineTo(-5, 15);
            ctx.moveTo(5, -15);
            ctx.lineTo(5, 15);
            ctx.stroke();

            // Connection lines
            ctx.beginPath();
            ctx.moveTo(-20, 0);
            ctx.lineTo(-5, 0);
            ctx.moveTo(5, 0);
            ctx.lineTo(20, 0);
            ctx.stroke();
        }

        function drawOpAmp(ctx) {
            ctx.beginPath();
            ctx.moveTo(-20, -15);
            ctx.lineTo(20, 0);
            ctx.lineTo(-20, 15);
            ctx.closePath();
            ctx.stroke();

            // Input labels
            ctx.fillStyle = 'var(--text-primary)';
            ctx.font = '12px Inter';
            ctx.textAlign = 'center';
            ctx.fillText('+', -10, -5);
            ctx.fillText('-', -10, 10);
        }

        function drawFunctionGenerator(ctx) {
            ctx.beginPath();
            ctx.arc(0, 0, 20, 0, 2 * Math.PI);
            ctx.stroke();

            // Sine wave symbol
            ctx.beginPath();
            for (let i = -10; i <= 10; i++) {
                const y = 8 * Math.sin(i * 0.5);
                if (i === -10) ctx.moveTo(i, y);
                else ctx.lineTo(i, y);
            }
            ctx.stroke();
        }

        function drawBioSignal(ctx) {
            ctx.beginPath();
            ctx.rect(-20, -15, 40, 30);
            ctx.stroke();

            // Heart symbol
            ctx.fillStyle = 'var(--accent-red)';
            ctx.font = '16px Inter';
            ctx.textAlign = 'center';
            ctx.fillText('💓', 0, 5);
        }

        function drawOscilloscope(ctx) {
            ctx.beginPath();
            ctx.rect(-20, -15, 40, 30);
            ctx.stroke();

            // Screen
            ctx.beginPath();
            ctx.rect(-15, -10, 30, 20);
            ctx.stroke();

            // Waveform
            ctx.beginPath();
            for (let i = -12; i <= 12; i += 2) {
                const y = 5 * Math.sin(i * 0.5);
                if (i === -12) ctx.moveTo(i, y);
                else ctx.lineTo(i, y);
            }
            ctx.stroke();
        }

        function drawConnection(ctx, connection) {
            // Simplified connection drawing
            const fromComp = AppState.circuitSimulation.components.find(c => connection.from.startsWith(c.id));
            const toComp = AppState.circuitSimulation.components.find(c => connection.to.startsWith(c.id));

            if (fromComp && toComp) {
                ctx.strokeStyle = 'var(--accent-yellow)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromComp.x + 20, fromComp.y);
                ctx.lineTo(toComp.x - 20, toComp.y);
                ctx.stroke();
            }
        }

        function runCircuitSimulation() {
            AppState.circuitSimulation.running = true;
            document.getElementById('runSimBtn').disabled = true;
            document.getElementById('stopSimBtn').disabled = false;
            document.getElementById('simStatus').textContent = 'Running';

            // Start analysis canvas animation
            startAnalysisAnimation();
        }

        function stopCircuitSimulation() {
            AppState.circuitSimulation.running = false;
            document.getElementById('runSimBtn').disabled = false;
            document.getElementById('stopSimBtn').disabled = true;
            document.getElementById('simStatus').textContent = 'Stopped';

            // Stop analysis animation
            if (AppState.animationFrameId) {
                cancelAnimationFrame(AppState.animationFrameId);
            }
        }

        function startAnalysisAnimation() {
            const canvas = document.getElementById('analysisCanvas');
            const ctx = canvas.getContext('2d');

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            function animate() {
                if (!AppState.circuitSimulation.running) return;

                // Clear canvas
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw grid
                drawAnalysisGrid(ctx, canvas.width, canvas.height);

                // Draw simulated waveforms
                drawSimulatedWaveforms(ctx, canvas.width, canvas.height);

                AppState.animationFrameId = requestAnimationFrame(animate);
            }

            animate();
        }

        function drawAnalysisGrid(ctx, width, height) {
            ctx.strokeStyle = '#1a2332';
            ctx.lineWidth = 1;

            for (let x = 0; x <= width; x += 30) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y <= height; y += 20) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        }

        function drawSimulatedWaveforms(ctx, width, height) {
            const time = Date.now() * 0.001;
            const centerY = height / 2;

            // Find function generator
            const funcGen = AppState.circuitSimulation.components.find(c => c.type === 'function-generator');
            if (!funcGen) return;

            const freq = funcGen.properties.frequency || 1000;
            const amp = funcGen.properties.amplitude || 1;

            // Input signal
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY - amp * 30 * Math.sin(2 * Math.PI * freq * t * 0.001);

                if (x === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();

            // Output signal (simulated)
            const gain = simulateCircuitGain(freq);
            ctx.strokeStyle = '#fbbf24';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 + time;
                const y = centerY - amp * gain * 30 * Math.sin(2 * Math.PI * freq * t * 0.001);

                if (x === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();
        }

        function simulateCircuitGain(frequency) {
            // Simple circuit simulation based on components
            const resistors = AppState.circuitSimulation.components.filter(c => c.type === 'resistor');
            const capacitors = AppState.circuitSimulation.components.filter(c => c.type === 'capacitor');
            const opAmps = AppState.circuitSimulation.components.filter(c => c.type === 'op-amp');

            let gain = 1;

            // Op-amp gain
            if (opAmps.length > 0) {
                gain *= opAmps[0].properties.gain || 1;
            }

            // RC filter simulation
            if (resistors.length > 0 && capacitors.length > 0) {
                const R = resistors[0].properties.resistance || 1000;
                const C = capacitors[0].properties.capacitance * 1e-6 || 1e-6;
                const cutoffFreq = 1 / (2 * Math.PI * R * C);

                // Low-pass filter response
                gain *= 1 / Math.sqrt(1 + Math.pow(frequency / cutoffFreq, 2));
            }

            return Math.min(gain, 10); // Limit maximum gain
        }

        function generateBodePlot() {
            const canvas = document.getElementById('analysisCanvas');
            const ctx = canvas.getContext('2d');

            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw axes
            drawBodePlotAxes(ctx, canvas.width, canvas.height);

            // Calculate and draw Bode plot
            drawBodePlotCurve(ctx, canvas.width, canvas.height);

            // Hide placeholder
            document.getElementById('analysisPlaceholder').style.display = 'none';
        }

        function drawBodePlotAxes(ctx, width, height) {
            ctx.strokeStyle = 'var(--text-secondary)';
            ctx.lineWidth = 1;

            // X-axis (frequency)
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 20, height - 50);
            ctx.stroke();

            // Y-axis (magnitude)
            ctx.beginPath();
            ctx.moveTo(50, 20);
            ctx.lineTo(50, height - 50);
            ctx.stroke();

            // Labels
            ctx.fillStyle = 'var(--text-secondary)';
            ctx.font = '12px Inter';
            ctx.textAlign = 'center';
            ctx.fillText('Frequency (Hz)', width / 2, height - 10);

            ctx.save();
            ctx.translate(15, height / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.fillText('Magnitude (dB)', 0, 0);
            ctx.restore();
        }

        function drawBodePlotCurve(ctx, width, height) {
            ctx.strokeStyle = 'var(--accent-cyan)';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const startFreq = 1;
            const endFreq = 10000;
            const plotWidth = width - 70;
            const plotHeight = height - 70;

            let firstPoint = true;

            for (let i = 0; i <= 100; i++) {
                const logFreq = Math.log10(startFreq) + (i / 100) * (Math.log10(endFreq) - Math.log10(startFreq));
                const freq = Math.pow(10, logFreq);
                const gain = simulateCircuitGain(freq);
                const gainDB = 20 * Math.log10(Math.abs(gain));

                const x = 50 + (i / 100) * plotWidth;
                const y = height - 50 - ((gainDB + 40) / 80) * plotHeight; // Normalize to -40 to +40 dB range

                if (firstPoint) {
                    ctx.moveTo(x, y);
                    firstPoint = false;
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();
        }

        function showAnalysisTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.analysis-tab').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
                btn.style.color = 'var(--text-secondary)';
            });

            event.target.classList.add('active');
            event.target.style.borderBottomColor = 'var(--accent-cyan)';
            event.target.style.color = 'var(--accent-cyan)';

            if (tabName === 'bode') {
                generateBodePlot();
            } else {
                document.getElementById('analysisPlaceholder').style.display = 'block';
            }
        }

        function clearCanvas() {
            AppState.circuitSimulation.components = [];
            AppState.circuitSimulation.connections = [];
            drawCircuitCanvas();
            updateCircuitInfo();
        }

        function updateCircuitInfo() {
            document.getElementById('componentCount').textContent = AppState.circuitSimulation.components.length;
            document.getElementById('connectionCount').textContent = AppState.circuitSimulation.connections.length;
        }

        function showComponentProperties(component) {
            const panel = document.getElementById('propertiesPanel');
            const content = document.getElementById('propertiesContent');

            content.innerHTML = '';

            Object.keys(component.properties).forEach(prop => {
                const div = document.createElement('div');
                div.style.marginBottom = '0.5rem';

                const label = document.createElement('label');
                label.textContent = prop.charAt(0).toUpperCase() + prop.slice(1) + ':';
                label.style.display = 'block';
                label.style.fontSize = '0.85rem';
                label.style.marginBottom = '0.25rem';

                const input = document.createElement('input');
                input.type = 'number';
                input.value = component.properties[prop];
                input.style.width = '100%';
                input.style.padding = '0.5rem';
                input.style.border = '1px solid var(--border-color)';
                input.style.borderRadius = '4px';
                input.style.background = 'var(--bg-secondary)';
                input.style.color = 'var(--text-primary)';
                input.dataset.property = prop;

                div.appendChild(label);
                div.appendChild(input);
                content.appendChild(div);
            });

            panel.classList.remove('hidden');
            panel.dataset.componentId = component.id;
        }

        function applyProperties() {
            const panel = document.getElementById('propertiesPanel');
            const componentId = panel.dataset.componentId;
            const component = AppState.circuitSimulation.components.find(c => c.id === componentId);

            if (component) {
                const inputs = panel.querySelectorAll('input');
                inputs.forEach(input => {
                    const prop = input.dataset.property;
                    component.properties[prop] = parseFloat(input.value) || 0;
                });

                drawCircuitCanvas();
            }

            closeProperties();
        }

        function closeProperties() {
            document.getElementById('propertiesPanel').classList.add('hidden');
        }

        // Utility Functions
        function exportData() {
            if (!AppState.selectedModule) {
                alert('Please select a measurement module first.');
                return;
            }

            const module = LabModules[AppState.selectedModule];
            let csvContent = 'Time(s),Voltage(V)\n';
            const sampleRate = 1000;
            const duration = 10;

            for (let i = 0; i < duration * sampleRate; i++) {
                const time = i / sampleRate;
                let signal = 0;

                switch (module.name.split(' ')[0]) {
                    case 'ECG':
                        signal = generateECGWave(time) * module.amplitude;
                        break;
                    case 'EMG':
                        signal = generateEMGWave(time) * module.amplitude;
                        break;
                    case 'EEG':
                        signal = generateEEGWave(time) * module.amplitude;
                        break;
                    default:
                        signal = Math.sin(2 * Math.PI * module.frequency * time) * module.amplitude;
                }

                // Add noise
                signal += (Math.random() - 0.5) * 0.01;

                csvContent += `${time.toFixed(3)},${signal.toFixed(6)}\n`;
            }

            // Download file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${module.name.replace(/\s+/g, '_')}_data.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function showHelp() {
            document.getElementById('helpModal').classList.remove('hidden');
        }

        function hideHelp() {
            document.getElementById('helpModal').classList.add('hidden');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }

            // Close help modal when clicking outside
            if (event.target.id === 'helpModal') {
                hideHelp();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize simulation lab
            updateSensorGrid();
            updateTabContent();

            // Set up oscilloscope canvas
            const oscilloscope = document.getElementById('oscilloscope');
            oscilloscope.width = oscilloscope.offsetWidth;
            oscilloscope.height = oscilloscope.offsetHeight;

            // Handle window resize
            window.addEventListener('resize', function() {
                if (AppState.currentMode === 'design') {
                    initializeCircuitCanvas();
                } else {
                    const oscilloscope = document.getElementById('oscilloscope');
                    oscilloscope.width = oscilloscope.offsetWidth;
                    oscilloscope.height = oscilloscope.offsetHeight;
                }
            });

            // Initialize with simulation mode
            switchMode('simulation');
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case '1':
                        event.preventDefault();
                        switchMode('simulation');
                        break;
                    case '2':
                        event.preventDefault();
                        switchMode('design');
                        break;
                    case 's':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            if (AppState.circuitSimulation.running) {
                                stopCircuitSimulation();
                            } else {
                                runCircuitSimulation();
                            }
                        }
                        break;
                    case 'c':
                        event.preventDefault();
                        if (AppState.currentMode === 'design') {
                            clearCanvas();
                        }
                        break;
                }
            }

            // Escape key to close modals and dropdowns
            if (event.key === 'Escape') {
                hideHelp();
                closeProperties();
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>
