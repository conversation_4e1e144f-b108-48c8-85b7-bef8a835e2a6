<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v2.0 - المعمل الافتراضي المحسن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #ffffff;
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .author-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 0.5rem;
            margin-top: 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.9rem;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .control-panel {
            background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border: 2px solid #475569;
        }

        .control-panel h2 {
            color: #60a5fa;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 1.3rem;
        }

        .power-section {
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 0.5rem;
        }

        .power-btn {
            background: linear-gradient(145deg, #dc2626 0%, #ef4444 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .power-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
        }

        .power-btn.on {
            background: linear-gradient(145deg, #059669 0%, #10b981 100%);
        }

        .control-group {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
        }

        .control-group h3 {
            color: #a78bfa;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .module-select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #475569;
            border-radius: 0.5rem;
            background: #374151;
            color: white;
            font-size: 0.9rem;
        }

        .module-select:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .sensors-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .sensor-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem;
        }

        .sensor-checkbox {
            width: 1rem;
            height: 1rem;
        }

        .sensor-checkbox:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .led-indicator {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background: #374151;
            border: 1px solid #6b7280;
            transition: all 0.3s;
        }

        .led-indicator.active {
            background: #10b981;
            box-shadow: 0 0 8px #10b981;
        }

        .slider-group {
            margin-bottom: 1rem;
        }

        .slider-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
            color: #d1d5db;
        }

        .slider {
            width: 100%;
            height: 0.5rem;
            border-radius: 0.25rem;
            background: #374151;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }

        .slider:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .circuit-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(145deg, #7c3aed 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .circuit-btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .circuit-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .safety-warning {
            background: rgba(239, 68, 68, 0.1);
            border: 2px solid #ef4444;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .safety-warning.show {
            display: block;
        }

        .display-area {
            background: linear-gradient(145deg, #111827 0%, #1f2937 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border: 2px solid #374151;
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.4);
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 0.9rem;
            color: #fbbf24;
        }

        .lcd-display {
            background: #000000;
            color: #00ff00;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
            border: 2px solid #374151;
            min-height: 6rem;
        }

        .oscilloscope {
            background: #000000;
            border: 2px solid #374151;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .oscilloscope canvas {
            width: 100%;
            height: 250px;
            border-radius: 0.5rem;
        }

        .tabs {
            display: flex;
            margin-bottom: 1rem;
            border-bottom: 2px solid #374151;
        }

        .tab {
            flex: 1;
            padding: 0.75rem;
            background: rgba(0, 0, 0, 0.2);
            border: none;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .tab.active {
            background: #3b82f6;
            color: white;
        }

        .tab-content {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 0.5rem;
            min-height: 8rem;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .export-btn {
            background: linear-gradient(145deg, #059669 0%, #10b981 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .export-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .sensors-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Bio-Signal Explorer v2.0: المعمل الافتراضي المحسن</h1>
        <p>محاكاة تفاعلية لأجهزة القياس الطبية الحيوية</p>
        <div class="author-info">
            Dr. Mohammed Yagoub Esmail, SUST-BME, @2025 | 
            البريد الإلكتروني: <EMAIL> | 
            الهاتف: +249912867327, +966538076790
        </div>
    </div>

    <div class="container">
        <!-- لوحة التحكم - العمود الأيسر -->
        <div class="control-panel">
            <h2>🔧 لوحة التحكم الرئيسية</h2>
            
            <!-- زر الطاقة -->
            <div class="power-section">
                <button class="power-btn" id="powerBtn">⚡ تشغيل النظام</button>
            </div>

            <!-- اختيار وحدة القياس -->
            <div class="control-group">
                <h3>📊 اختيار وحدة القياس</h3>
                <select class="module-select" id="moduleSelect" disabled>
                    <option value="">اختر وحدة القياس...</option>
                    <option value="ecg">ECG - تخطيط كهربية القلب</option>
                    <option value="emg">EMG - تخطيط كهربية العضلات</option>
                    <option value="eeg">EEG - تخطيط كهربية الدماغ</option>
                    <option value="eog">EOG - تخطيط كهربية العين</option>
                    <option value="plethysmography">قياس التدفق الدموي</option>
                    <option value="gsr">GSR - استجابة الجلد الكهربية</option>
                    <option value="temperature">قياس درجة الحرارة</option>
                    <option value="ph">قياس درجة الحموضة</option>
                    <option value="impedance">قياس مقاومة الجسم</option>
                </select>
            </div>

            <!-- محاكاة توصيل المستشعرات -->
            <div class="control-group">
                <h3>🔌 توصيل المستشعرات</h3>
                <div class="sensors-grid" id="sensorsGrid">
                    <!-- سيتم إنشاؤها ديناميكياً -->
                </div>
            </div>

            <!-- عناصر تحكم مولد الإشارات -->
            <div class="control-group">
                <h3>🎛️ مولد الإشارات</h3>
                <div class="slider-group">
                    <label>التردد: <span id="freqValue">100</span> Hz</label>
                    <input type="range" class="slider" id="freqSlider" min="1" max="1000" value="100" disabled>
                </div>
                <div class="slider-group">
                    <label>السعة: <span id="ampValue">1.0</span> Vpp</label>
                    <input type="range" class="slider" id="ampSlider" min="0.1" max="10" step="0.1" value="1.0" disabled>
                </div>
            </div>

            <!-- زر وضع تحليل الدائرة -->
            <div class="control-group">
                <button class="circuit-btn" id="circuitBtn" disabled>🔬 الدخول لوضع تحليل الدائرة</button>
            </div>

            <!-- تحذير السلامة -->
            <div class="safety-warning" id="safetyWarning">
                <h3>⚠️ تحذير السلامة</h3>
                <p>تأكد من فصل جميع الأقطاب عن المريض قبل إجراء قياسات المقاومة</p>
            </div>
        </div>

        <!-- منطقة العرض - العمود الأيمن -->
        <div class="display-area">
            <!-- لوحة الحالة -->
            <div class="status-panel" id="statusPanel">
                النظام متوقف عن التشغيل - يرجى الضغط على زر الطاقة
            </div>

            <!-- شاشة LCD -->
            <div class="lcd-display" id="lcdDisplay">
                > النظام غير نشط
                > يرجى تشغيل النظام أولاً
            </div>

            <!-- راسم الإشارة -->
            <div class="oscilloscope">
                <canvas id="oscilloscope" width="800" height="250"></canvas>
            </div>

            <!-- نظام التبويبات -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('principle')">المبدأ الفسيولوجي</button>
                <button class="tab" onclick="showTab('procedure')">خطوات التجربة</button>
                <button class="tab" onclick="showTab('export')">تصدير البيانات</button>
            </div>

            <div class="tab-content" id="tabContent">
                <!-- المحتوى سيتم تحديثه ديناميكياً -->
            </div>
        </div>
    </div>

    <script>
        // إدارة الحالة
        const state = {
            powerOn: false,
            currentModule: null,
            circuitMode: false,
            frequency: 100,
            amplitude: 1.0,
            activeTab: 'principle',
            connectedSensors: [],
            animationId: null
        };

        // بيانات وحدات القياس
        const labModules = {
            ecg: {
                name: "تخطيط كهربية القلب",
                requiredSensors: ['IN1', 'IN2', 'IN3'],
                waveform: 'ecg',
                principle: "يقيس النشاط الكهربائي للقلب عبر أقطاب موضوعة على الجلد. يعكس انتشار الإشارة الكهربائية عبر عضلة القلب.",
                procedure: "1. ضع الأقطاب على الصدر والأطراف\n2. تأكد من جودة التوصيل\n3. سجل الإشارة لمدة 10 ثوانٍ\n4. احسب معدل ضربات القلب",
                sampleRate: 1000,
                frequency: 1.2,
                amplitude: 1.0
            },
            emg: {
                name: "تخطيط كهربية العضلات",
                requiredSensors: ['IN1', 'IN2'],
                waveform: 'emg',
                principle: "يقيس النشاط الكهربائي للعضلات الهيكلية أثناء الانقباض والانبساط.",
                procedure: "1. ضع الأقطاب على العضلة المراد قياسها\n2. اطلب من المريض الانقباض التدريجي\n3. سجل الإشارة\n4. حلل سعة الإشارة",
                sampleRate: 2000,
                frequency: 150,
                amplitude: 0.5
            },
            eeg: {
                name: "تخطيط كهربية الدماغ",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                waveform: 'eeg',
                principle: "يقيس النشاط الكهربائي للدماغ عبر أقطاب موضوعة على فروة الرأس.",
                procedure: "1. ضع الأقطاب حسب نظام 10-20\n2. تأكد من المقاومة أقل من 5kΩ\n3. سجل في حالة الراحة\n4. حلل الترددات المختلفة",
                sampleRate: 500,
                frequency: 10,
                amplitude: 0.1
            },
            eog: {
                name: "تخطيط كهربية العين",
                requiredSensors: ['IN1', 'IN2'],
                waveform: 'eog',
                principle: "يقيس حركة العين بناءً على الفرق في الجهد بين القرنية والشبكية.",
                procedure: "1. ضع الأقطاب حول العين\n2. اطلب حركات محددة للعين\n3. سجل الإشارة\n4. حلل اتجاه الحركة",
                sampleRate: 250,
                frequency: 0.5,
                amplitude: 0.8
            },
            plethysmography: {
                name: "قياس التدفق الدموي",
                requiredSensors: ['IN1'],
                waveform: 'pleth',
                principle: "يقيس التغيرات في حجم الدم في الأوعية الدموية باستخدام الضوء.",
                procedure: "1. ضع المستشعر على الإصبع\n2. تأكد من عدم الحركة\n3. سجل الإشارة\n4. احسب معدل النبض",
                sampleRate: 100,
                frequency: 1.0,
                amplitude: 2.0
            },
            gsr: {
                name: "استجابة الجلد الكهربية",
                requiredSensors: ['IN1', 'IN2'],
                waveform: 'gsr',
                principle: "يقيس التغيرات في مقاومة الجلد الكهربائية نتيجة النشاط العصبي.",
                procedure: "1. ضع الأقطاب على الأصابع\n2. اطلب تمارين الاسترخاء\n3. سجل الاستجابة\n4. حلل التغيرات",
                sampleRate: 10,
                frequency: 0.1,
                amplitude: 0.3
            },
            temperature: {
                name: "قياس درجة الحرارة",
                requiredSensors: ['IN1'],
                waveform: 'temp',
                principle: "يقيس درجة حرارة الجسم باستخدام مستشعر حراري دقيق.",
                procedure: "1. ضع المستشعر في المكان المناسب\n2. انتظر الاستقرار\n3. سجل القراءة\n4. قارن مع القيم الطبيعية",
                sampleRate: 1,
                frequency: 0.01,
                amplitude: 37.0
            },
            ph: {
                name: "قياس درجة الحموضة",
                requiredSensors: ['IN1'],
                waveform: 'ph',
                principle: "يقيس تركيز أيونات الهيدروجين في السوائل الحيوية.",
                procedure: "1. معايرة المستشعر\n2. غمس المستشعر في العينة\n3. انتظار الاستقرار\n4. تسجيل القراءة",
                sampleRate: 1,
                frequency: 0.01,
                amplitude: 7.4
            },
            impedance: {
                name: "قياس مقاومة الجسم",
                requiredSensors: ['IN1', 'IN2', 'IN3', 'IN4'],
                waveform: 'impedance',
                principle: "يقيس المقاومة الكهربائية لأنسجة الجسم لتحديد التركيب الجسماني.",
                procedure: "1. تأكد من فصل جميع الأقطاب الأخرى\n2. ضع أقطاب القياس\n3. تطبيق تيار آمن\n4. حساب المقاومة",
                sampleRate: 50,
                frequency: 1000,
                amplitude: 0.001,
                warning: true
            }
        };

        // عناصر DOM
        const powerBtn = document.getElementById('powerBtn');
        const moduleSelect = document.getElementById('moduleSelect');
        const sensorsGrid = document.getElementById('sensorsGrid');
        const statusPanel = document.getElementById('statusPanel');
        const lcdDisplay = document.getElementById('lcdDisplay');
        const oscilloscope = document.getElementById('oscilloscope');
        const ctx = oscilloscope.getContext('2d');
        const freqSlider = document.getElementById('freqSlider');
        const ampSlider = document.getElementById('ampSlider');
        const freqValue = document.getElementById('freqValue');
        const ampValue = document.getElementById('ampValue');
        const circuitBtn = document.getElementById('circuitBtn');
        const safetyWarning = document.getElementById('safetyWarning');
        const tabContent = document.getElementById('tabContent');

        // تهيئة النظام
        function initializeSystem() {
            createSensorGrid();
            updateUI();
            showTab('principle');
            setupEventListeners();
        }

        // إنشاء شبكة المستشعرات
        function createSensorGrid() {
            const sensors = ['IN1', 'IN2', 'IN3', 'IN4', 'IN5', 'IN6', 'IN7'];
            sensorsGrid.innerHTML = '';
            
            sensors.forEach(sensor => {
                const sensorDiv = document.createElement('div');
                sensorDiv.className = 'sensor-item';
                sensorDiv.innerHTML = `
                    <input type="checkbox" class="sensor-checkbox" id="${sensor}" disabled>
                    <label for="${sensor}">${sensor}</label>
                    <div class="led-indicator" id="led-${sensor}"></div>
                `;
                sensorsGrid.appendChild(sensorDiv);
            });
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            powerBtn.addEventListener('click', togglePower);
            moduleSelect.addEventListener('change', selectModule);
            circuitBtn.addEventListener('click', toggleCircuitMode);
            
            freqSlider.addEventListener('input', (e) => {
                state.frequency = parseFloat(e.target.value);
                freqValue.textContent = state.frequency;
                updateUI();
            });
            
            ampSlider.addEventListener('input', (e) => {
                state.amplitude = parseFloat(e.target.value);
                ampValue.textContent = state.amplitude;
                updateUI();
            });

            // مستمعي أحداث المستشعرات
            document.querySelectorAll('.sensor-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSensorStatus);
            });
        }

        // تشغيل/إيقاف النظام
        function togglePower() {
            state.powerOn = !state.powerOn;
            
            if (state.powerOn) {
                powerBtn.textContent = '🔴 إيقاف النظام';
                powerBtn.classList.add('on');
                statusPanel.textContent = 'النظام نشط - يرجى اختيار وحدة القياس';
                statusPanel.style.color = '#10b981';
            } else {
                powerBtn.textContent = '⚡ تشغيل النظام';
                powerBtn.classList.remove('on');
                statusPanel.textContent = 'النظام متوقف عن التشغيل - يرجى الضغط على زر الطاقة';
                statusPanel.style.color = '#fbbf24';
                moduleSelect.value = '';
                state.currentModule = null;
                state.circuitMode = false;
            }
            
            updateUI();
        }

        // اختيار وحدة القياس
        function selectModule() {
            const selectedModule = moduleSelect.value;
            state.currentModule = selectedModule ? labModules[selectedModule] : null;
            
            if (selectedModule === 'impedance') {
                safetyWarning.classList.add('show');
            } else {
                safetyWarning.classList.remove('show');
            }
            
            updateUI();
            updateTabContent();
        }

        // تحديث حالة المستشعرات
        function updateSensorStatus() {
            const connectedSensors = [];
            document.querySelectorAll('.sensor-checkbox:checked').forEach(checkbox => {
                connectedSensors.push(checkbox.id);
            });
            state.connectedSensors = connectedSensors;
            updateLEDs();
            updateUI();
        }

        // تحديث مؤشرات LED
        function updateLEDs() {
            document.querySelectorAll('.led-indicator').forEach(led => {
                led.classList.remove('active');
            });
            
            if (state.currentModule) {
                const requiredSensors = state.currentModule.requiredSensors;
                state.connectedSensors.forEach(sensor => {
                    if (requiredSensors.includes(sensor)) {
                        document.getElementById(`led-${sensor}`).classList.add('active');
                    }
                });
            }
        }

        // تبديل وضع تحليل الدائرة
        function toggleCircuitMode() {
            state.circuitMode = !state.circuitMode;
            
            if (state.circuitMode) {
                circuitBtn.textContent = '📊 العودة لوضع القياس الفسيولوجي';
                circuitBtn.style.background = 'linear-gradient(145deg, #dc2626 0%, #ef4444 100%)';
            } else {
                circuitBtn.textContent = '🔬 الدخول لوضع تحليل الدائرة';
                circuitBtn.style.background = 'linear-gradient(145deg, #7c3aed 0%, #8b5cf6 100%)';
            }
            
            updateUI();
        }

        // تحديث واجهة المستخدم
        function updateUI() {
            // تمكين/تعطيل العناصر
            moduleSelect.disabled = !state.powerOn;
            freqSlider.disabled = !state.powerOn;
            ampSlider.disabled = !state.powerOn;
            
            document.querySelectorAll('.sensor-checkbox').forEach(checkbox => {
                checkbox.disabled = !state.powerOn;
            });
            
            circuitBtn.disabled = !state.powerOn || !state.currentModule;
            
            // تحديث شاشة LCD
            updateLCD();
            
            // تحديث راسم الإشارة
            updateOscilloscope();
            
            // تحديث مؤشرات LED
            updateLEDs();
        }

        // تحديث شاشة LCD
        function updateLCD() {
            if (!state.powerOn) {
                lcdDisplay.innerHTML = '> النظام غير نشط<br>> يرجى تشغيل النظام أولاً';
                return;
            }
            
            if (!state.currentModule) {
                lcdDisplay.innerHTML = '> النظام نشط<br>> يرجى اختيار وحدة القياس';
                return;
            }
            
            const requiredSensors = state.currentModule.requiredSensors;
            const connectedRequired = state.connectedSensors.filter(s => requiredSensors.includes(s));
            const allConnected = connectedRequired.length === requiredSensors.length;
            
            let content = `> وحدة: ${state.currentModule.name}<br>`;
            content += `> المستشعرات: ${connectedRequired.length}/${requiredSensors.length}<br>`;
            
            if (state.circuitMode) {
                content += `> وضع: تحليل الدائرة<br>`;
                content += `> التردد: ${state.frequency} Hz<br>`;
                content += `> السعة: ${state.amplitude} Vpp<br>`;
                
                if (allConnected) {
                    const gain = calculateGain(state.frequency);
                    content += `> الكسب: ${gain.toFixed(2)} dB`;
                }
            } else {
                content += `> وضع: قياس فسيولوجي<br>`;
                
                if (allConnected) {
                    // حساب قيم وهمية للعرض
                    const mockValue = getMockMeasurement();
                    content += `> القياس: ${mockValue}`;
                } else {
                    content += `> يرجى توصيل المستشعرات`;
                }
            }
            
            lcdDisplay.innerHTML = content;
        }

        // حساب الكسب (وهمي)
        function calculateGain(frequency) {
            if (!state.currentModule) return 0;
            
            // محاكاة استجابة فلتر تمرير منخفض
            const cutoffFreq = 100; // Hz
            const gain = 20 * Math.log10(1 / Math.sqrt(1 + Math.pow(frequency / cutoffFreq, 2)));
            return gain;
        }

        // الحصول على قياس وهمي
        function getMockMeasurement() {
            if (!state.currentModule) return '';
            
            switch (state.currentModule.name) {
                case 'تخطيط كهربية القلب':
                    return `${60 + Math.round(Math.random() * 40)} BPM`;
                case 'تخطيط كهربية العضلات':
                    return `${(Math.random() * 2).toFixed(2)} mV`;
                case 'تخطيط كهربية الدماغ':
                    return `${(Math.random() * 100).toFixed(1)} µV`;
                case 'قياس درجة الحرارة':
                    return `${(36.5 + Math.random()).toFixed(1)} °C`;
                case 'قياس درجة الحموضة':
                    return `${(7.35 + Math.random() * 0.1).toFixed(2)} pH`;
                default:
                    return `${(Math.random() * 10).toFixed(2)} units`;
            }
        }

        // تحديث راسم الإشارة
        function updateOscilloscope() {
            if (state.animationId) {
                cancelAnimationFrame(state.animationId);
            }
            
            if (state.powerOn && state.currentModule) {
                drawWaveform();
            } else {
                clearOscilloscope();
            }
        }

        // مسح راسم الإشارة
        function clearOscilloscope() {
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, oscilloscope.width, oscilloscope.height);
            
            // رسم الشبكة
            drawGrid();
            
            // رسم نص
            ctx.fillStyle = '#404040';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('راسم الإشارة غير نشط', oscilloscope.width / 2, oscilloscope.height / 2);
        }

        // رسم الشبكة
        function drawGrid() {
            ctx.strokeStyle = '#202020';
            ctx.lineWidth = 1;
            
            // خطوط عمودية
            for (let x = 0; x <= oscilloscope.width; x += 40) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, oscilloscope.height);
                ctx.stroke();
            }
            
            // خطوط أفقية
            for (let y = 0; y <= oscilloscope.height; y += 25) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(oscilloscope.width, y);
                ctx.stroke();
            }
        }

        // رسم الموجة
        function drawWaveform() {
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, oscilloscope.width, oscilloscope.height);
            
            drawGrid();
            
            const time = Date.now() * 0.001;
            const centerY = oscilloscope.height / 2;
            
            if (state.circuitMode) {
                // رسم موجتين: الإدخال والإخراج
                drawSineWave(time, state.frequency, state.amplitude, '#60a5fa', 'إدخال');
                
                const outputAmplitude = state.amplitude * Math.pow(10, calculateGain(state.frequency) / 20);
                drawSineWave(time, state.frequency, outputAmplitude, '#fbbf24', 'إخراج');
                
                // عرض الكسب
                ctx.fillStyle = '#ffffff';
                ctx.font = '14px Arial';
                ctx.textAlign = 'right';
                ctx.fillText(`الكسب: ${calculateGain(state.frequency).toFixed(2)} dB`, oscilloscope.width - 10, 20);
            } else {
                // رسم الإشارة الفسيولوجية
                drawBioSignal(time);
            }
            
            state.animationId = requestAnimationFrame(drawWaveform);
        }

        // رسم موجة جيبية
        function drawSineWave(time, frequency, amplitude, color, label) {
            const centerY = oscilloscope.height / 2;
            const scale = 50;
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = 0; x < oscilloscope.width; x++) {
                const t = (x / oscilloscope.width) * 4 + time;
                const y = centerY - amplitude * scale * Math.sin(2 * Math.PI * frequency * t * 0.1);
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            // تسمية الموجة
            ctx.fillStyle = color;
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(label, 10, color === '#60a5fa' ? 20 : 35);
        }

        // رسم الإشارة الحيوية
        function drawBioSignal(time) {
            const centerY = oscilloscope.height / 2;
            const scale = 80;
            
            ctx.strokeStyle = '#10b981';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = 0; x < oscilloscope.width; x++) {
                const t = (x / oscilloscope.width) * 6 + time;
                let y = centerY;
                
                // محاكاة أشكال موجات مختلفة
                switch (state.currentModule.waveform) {
                    case 'ecg':
                        y = centerY - scale * generateECGWave(t);
                        break;
                    case 'emg':
                        y = centerY - scale * generateEMGWave(t);
                        break;
                    case 'eeg':
                        y = centerY - scale * generateEEGWave(t);
                        break;
                    case 'eog':
                        y = centerY - scale * generateEOGWave(t);
                        break;
                    default:
                        y = centerY - scale * Math.sin(2 * Math.PI * state.currentModule.frequency * t) * state.currentModule.amplitude;
                }
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            // تسمية الإشارة
            ctx.fillStyle = '#10b981';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(state.currentModule.name, 10, 20);
        }

        // توليد موجة ECG
        function generateECGWave(t) {
            const heartRate = 1.2;
            const phase = (t * heartRate) % 1;
            
            if (phase < 0.1) {
                return 0.1 * Math.sin(phase * 10 * Math.PI);
            } else if (phase < 0.2) {
                return -0.3 * Math.sin((phase - 0.1) * 10 * Math.PI);
            } else if (phase < 0.3) {
                return 1.0 * Math.sin((phase - 0.2) * 10 * Math.PI);
            } else if (phase < 0.4) {
                return 0.3 * Math.sin((phase - 0.3) * 10 * Math.PI);
            } else {
                return 0.05 * Math.sin((phase - 0.4) * 5 * Math.PI);
            }
        }

        // توليد موجة EMG
        function generateEMGWave(t) {
            return (Math.random() - 0.5) * 0.5 * (1 + Math.sin(t * 2));
        }

        // توليد موجة EEG
        function generateEEGWave(t) {
            return 0.1 * (Math.sin(t * 8) + 0.5 * Math.sin(t * 12) + 0.3 * Math.sin(t * 20)) + (Math.random() - 0.5) * 0.05;
        }

        // توليد موجة EOG
        function generateEOGWave(t) {
            const saccade = Math.floor(t * 0.5) % 3;
            if (saccade === 0) {
                return 0.8 * Math.sin(t * 0.5);
            } else {
                return 0.1 * Math.sin(t * 2) + (Math.random() - 0.5) * 0.1;
            }
        }

        // إدارة التبويبات
        function showTab(tabName) {
            state.activeTab = tabName;
            
            // تحديث أزرار التبويبات
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.textContent.includes(getTabDisplayName(tabName))) {
                    tab.classList.add('active');
                }
            });
            
            updateTabContent();
        }

        // الحصول على اسم التبويب للعرض
        function getTabDisplayName(tabName) {
            switch (tabName) {
                case 'principle':
                    return 'المبدأ الفسيولوجي';
                case 'procedure':
                    return 'خطوات التجربة';
                case 'export':
                    return 'تصدير البيانات';
                default:
                    return '';
            }
        }

        // تحديث محتوى التبويبات
        function updateTabContent() {
            let content = '';
            
            switch (state.activeTab) {
                case 'principle':
                    content = state.currentModule 
                        ? `<h3>المبدأ الفسيولوجي</h3><p>${state.currentModule.principle}</p>`
                        : '<p>يرجى اختيار وحدة قياس لعرض المبدأ الفسيولوجي</p>';
                    break;
                    
                case 'procedure':
                    content = state.currentModule 
                        ? `<h3>خطوات التجربة</h3><pre>${state.currentModule.procedure}</pre>`
                        : '<p>يرجى اختيار وحدة قياس لعرض خطوات التجربة</p>';
                    break;
                    
                case 'export':
                    content = `
                        <h3>تصدير البيانات</h3>
                        <p>يمكنك تنزيل عينة من البيانات المسجلة بصيغة CSV:</p>
                        <button class="export-btn" onclick="exportData()">📥 تنزيل عينة البيانات (.csv)</button>
                    `;
                    break;
            }
            
            tabContent.innerHTML = content;
        }

        // تصدير البيانات
        function exportData() {
            if (!state.currentModule) {
                alert('يرجى اختيار وحدة قياس أولاً');
                return;
            }
            
            // إنشاء بيانات وهمية
            let csvContent = 'Time(s),Signal(V)\n';
            const sampleRate = state.currentModule.sampleRate || 1000;
            const duration = 10; // 10 ثوان
            
            for (let i = 0; i < duration * sampleRate; i++) {
                const time = i / sampleRate;
                const signal = Math.sin(2 * Math.PI * state.currentModule.frequency * time) * state.currentModule.amplitude + (Math.random() - 0.5) * 0.1;
                csvContent += `${time.toFixed(3)},${signal.toFixed(6)}\n`;
            }
            
            // إنشاء الملف وتنزيله
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${state.currentModule.name}_data.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>