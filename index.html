<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bio-Signal Explorer v2.0 - Interactive Virtual Lab</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Page-specific styles */
        :root {
            --primary-bg: #0f172a;
            --secondary-bg: #1e293b;
            --accent-blue: #3b82f6;
            --accent-cyan: #06b6d4;
            --accent-green: #10b981;
            --accent-purple: #8b5cf6;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
            --card-bg: #1e293b;
            --hover-bg: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.95;
            margin-bottom: 1rem;
        }

        .author-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            margin-top: 1rem;
            border-radius: 0.75rem;
            font-size: 0.95rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-menu {
            background: var(--card-bg);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 4rem;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 800px;
            margin: 0 auto 2rem;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-cyan));
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--hover-bg);
            border-color: var(--accent-cyan);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-cyan));
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: var(--accent-cyan);
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-cyan));
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .lab-modules {
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .module-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            border-color: var(--accent-green);
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .module-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(45deg, var(--accent-green), var(--accent-cyan));
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .module-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .module-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .module-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: var(--accent-green);
        }

        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            background: var(--accent-green);
            border-radius: 50%;
        }

        .footer {
            background: var(--card-bg);
            border-top: 1px solid var(--border-color);
            padding: 3rem 2rem 2rem;
            margin-top: 4rem;
        }

        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            text-align: center;
        }

        .footer-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .footer-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--accent-cyan);
        }

        .footer-bottom {
            border-top: 1px solid var(--border-color);
            padding-top: 2rem;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .hero-title {
                font-size: 2rem;
            }

            .features-grid,
            .modules-grid {
                grid-template-columns: 1fr;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .nav-links {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>Bio-Signal Explorer v2.0</h1>
            <p>Interactive Virtual Laboratory for Biomedical Signal Processing</p>
            <div class="author-info">
                <strong>Dr. Mohammed Yagoub Esmail</strong> | SUST-BME | 2025<br>
                📧 <EMAIL> | 📱 +249912867327, +966538076790
            </div>
        </div>
    </div>

    <nav class="nav-menu">
        <ul class="nav-links">
            <li><a href="#home" class="active">Home</a></li>
            <li><a href="#features">Features</a></li>
            <li><a href="#modules">Lab Modules</a></li>
            <li><a href="#about">About</a></li>
        </ul>
    </nav>

    <main class="container" id="home">
        <!-- Hero Section -->
        <section class="hero-section fade-in">
            <h2 class="hero-title">Welcome to the Future of Biomedical Education</h2>
            <p class="hero-description">
                Experience cutting-edge biomedical signal processing through our interactive virtual laboratory.
                Learn ECG, EMG, EEG, and other biosignal measurements without the need for physical hardware.
            </p>
            <div class="cta-buttons">
                <a href="Bio-Signal Explorer v3.html" class="btn btn-primary">
                    🚀 Launch Advanced Lab v3.0
                </a>
                <a href="Bio-Signal Explorer v2.html" class="btn btn-secondary">
                    🔬 Classic Lab v2.0
                </a>
                <a href="#features" class="btn btn-secondary">
                    📖 Learn More
                </a>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="features-grid fade-in">
            <div class="feature-card">
                <div class="feature-icon">🔬</div>
                <h3 class="feature-title">Interactive Simulations</h3>
                <p class="feature-description">
                    Realistic biomedical instrument simulations with real-time signal processing and visualization.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">Multiple Signal Types</h3>
                <p class="feature-description">
                    Support for ECG, EMG, EEG, EOG, and other physiological signals with authentic waveform patterns.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎛️</div>
                <h3 class="feature-title">Circuit Analysis Mode</h3>
                <p class="feature-description">
                    Advanced circuit analysis capabilities with frequency response and filter characteristics.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">💾</div>
                <h3 class="feature-title">Data Export</h3>
                <p class="feature-description">
                    Export measurement data in standard formats for further analysis and research.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <h3 class="feature-title">Web-Based Platform</h3>
                <p class="feature-description">
                    No installation required. Access the lab from any modern web browser on any device.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎓</div>
                <h3 class="feature-title">Educational Focus</h3>
                <p class="feature-description">
                    Designed specifically for biomedical engineering students and educators.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Circuit Design Toolkit</h3>
                <p class="feature-description">
                    NEW! Build and analyze circuits from scratch with drag-and-drop components and real-time simulation.
                </p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3 class="feature-title">Internal Circuit Viewer</h3>
                <p class="feature-description">
                    Explore the internal circuitry of each measurement module for deeper understanding.
                </p>
            </div>
        </section>

        <!-- Lab Modules Section -->
        <section id="modules" class="lab-modules fade-in">
            <h2 class="section-title">Available Lab Modules</h2>
            <div class="modules-grid">
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">❤️</div>
                        <div class="module-name">ECG Measurement</div>
                    </div>
                    <p class="module-description">
                        Electrocardiogram measurement and analysis with lead configuration and heart rate calculation.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">💪</div>
                        <div class="module-name">EMG Measurement</div>
                    </div>
                    <p class="module-description">
                        Electromyography for muscle activity measurement and force estimation.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">🧠</div>
                        <div class="module-name">EEG Measurement</div>
                    </div>
                    <p class="module-description">
                        Electroencephalography for brain activity monitoring and frequency analysis.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">👁️</div>
                        <div class="module-name">EOG Measurement</div>
                    </div>
                    <p class="module-description">
                        Electrooculography for eye movement tracking and gaze analysis.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">🩺</div>
                        <div class="module-name">Blood Pressure</div>
                    </div>
                    <p class="module-description">
                        Oscillometric blood pressure measurement with systolic and diastolic readings.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
                <div class="module-card" onclick="window.location.href='Bio-Signal Explorer v3.html'">
                    <div class="module-header">
                        <div class="module-icon">⚡</div>
                        <div class="module-name">Body Impedance</div>
                    </div>
                    <p class="module-description">
                        Bioelectrical impedance analysis for body composition and fluid monitoring.
                    </p>
                    <div class="module-status">
                        <span class="status-dot"></span>
                        Available
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="about" class="footer">
        <div class="footer-content">
            <h3 class="footer-title">About Bio-Signal Explorer v2.0</h3>
            <p class="footer-description">
                This virtual laboratory was developed to provide students and educators with an accessible platform
                for learning biomedical signal processing concepts without requiring expensive physical equipment.
            </p>
            <div class="footer-links">
                <a href="#home">Home</a>
                <a href="#features">Features</a>
                <a href="#modules">Lab Modules</a>
                <a href="Bio-Signal Explorer v3.html">Advanced Lab v3.0</a>
                <a href="Bio-Signal Explorer v2.html">Classic Lab v2.0</a>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail | Sudan University of Science and Technology</p>
                <p>Biomedical Engineering Department | Educational Use</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            // Handle navigation clicks
            const navLinks = document.querySelectorAll('.nav-links a, .footer-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = this.getAttribute('href');
                        const targetElement = document.querySelector(targetId);
                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }

                        // Update active nav link
                        navLinks.forEach(l => l.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });

            // Add fade-in animation on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all fade-in elements
            document.querySelectorAll('.fade-in').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            // Add pulse animation to CTA button
            const ctaButton = document.querySelector('.btn-primary');
            if (ctaButton) {
                ctaButton.classList.add('pulse');
            }

            // Module card hover effects
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-3px) scale(1)';
                });
            });

            // Feature card stagger animation
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

    </script>
    <script src="common.js"></script>
</body>
</html>