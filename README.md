# Bio-Signal Explorer v2.0 - Interactive Virtual Laboratory

## Overview

Bio-Signal Explorer v2.0 is an advanced web-based virtual laboratory designed for biomedical engineering education. This interactive platform simulates various biomedical instrumentation systems, allowing students to learn signal processing concepts without requiring physical hardware.

## Features

### 🔬 Interactive Simulations
- Realistic biomedical instrument simulations
- Real-time signal processing and visualization
- Multiple measurement modules

### 📊 Supported Signal Types
- **ECG (Electrocardiogram)**: Heart electrical activity monitoring
- **EMG (Electromyography)**: Muscle activity measurement
- **EEG (Electroencephalography)**: Brain wave monitoring
- **EOG (Electrooculography)**: Eye movement tracking
- **Blood Pressure**: Oscillometric measurement
- **Body Impedance**: Bioelectrical impedance analysis

### 🎛️ Advanced Features
- Circuit analysis mode with frequency response
- Function generator with adjustable parameters
- Sensor connection simulation
- Real-time waveform visualization
- Data export capabilities

### 🌐 Web-Based Platform
- No installation required
- Cross-platform compatibility
- Modern responsive design
- Accessible from any web browser

## File Structure

```
Bio-Signal Explorer v2.0/
├── index.html                          # Main landing page
├── Bio-Signal Explorer v2.html         # Enhanced virtual lab interface
├── Bio-Signal Explorer Virtual Lab.html # Alternative lab interface
├── styles.css                          # Main stylesheet
├── common.js                           # Common JavaScript functions
└── README.md                           # This documentation
```

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- JavaScript enabled
- No additional software installation required

### Running the Application

1. **Open the Landing Page**
   - Open `index.html` in your web browser
   - Navigate through the features and module descriptions
   - Click "Launch Virtual Lab" to start

2. **Using the Virtual Lab**
   - Click "Power On" to initialize the system
   - Select a measurement module from the dropdown
   - Connect required sensors by checking the appropriate boxes
   - Observe real-time signal visualization
   - Switch to circuit analysis mode for frequency response studies

3. **Exporting Data**
   - Navigate to the "Data Output" tab
   - Click "Download Sample Data" to export CSV files
   - Use exported data for offline analysis

## Educational Applications

### For Students
- Learn biomedical signal processing concepts
- Understand instrumentation principles
- Practice with realistic simulations
- Export data for analysis projects

### For Educators
- Demonstrate complex concepts visually
- Provide hands-on experience without equipment costs
- Assign virtual lab exercises
- Supplement traditional laboratory sessions

## Technical Details

### Technologies Used
- **HTML5**: Structure and semantic markup
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **JavaScript (ES6+)**: Interactive functionality and animations
- **Canvas API**: Real-time signal visualization
- **Web APIs**: File download and data export

### Browser Compatibility
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Performance Considerations
- Optimized animations using requestAnimationFrame
- Efficient canvas rendering
- Responsive design for various screen sizes
- Minimal external dependencies

## Module Descriptions

### ECG Measurement
- **Purpose**: Monitor heart electrical activity
- **Required Sensors**: IN1, IN2, IN3, IN5
- **Applications**: Heart rate calculation, arrhythmia detection
- **Waveform**: P-QRS-T complex simulation

### EMG Measurement
- **Purpose**: Measure muscle electrical activity
- **Required Sensors**: IN1, IN2, IN5
- **Applications**: Muscle force estimation, rehabilitation
- **Waveform**: Random muscle activation patterns

### EEG Measurement
- **Purpose**: Monitor brain electrical activity
- **Required Sensors**: IN1, IN2, IN3, IN4
- **Applications**: Sleep studies, cognitive research
- **Waveform**: Alpha, beta, theta wave simulation

### EOG Measurement
- **Purpose**: Track eye movements
- **Required Sensors**: IN1, IN2, IN3, IN4, IN5
- **Applications**: Gaze tracking, sleep studies
- **Waveform**: Saccadic eye movement patterns

### Blood Pressure Measurement
- **Purpose**: Measure arterial blood pressure
- **Required Sensors**: IN6
- **Applications**: Cardiovascular assessment
- **Method**: Oscillometric technique simulation

### Body Impedance Analysis
- **Purpose**: Analyze body composition
- **Required Sensors**: IN1, IN2, IN3, IN4
- **Applications**: Body fat measurement, fluid monitoring
- **Safety**: Includes safety warnings for pacemaker users

## Safety Information

⚠️ **Important Notice**: This is a virtual simulation for educational purposes only. When working with real biomedical equipment:

- Always follow proper safety protocols
- Ensure equipment calibration and maintenance
- Follow institutional guidelines for human subjects
- Obtain proper training before using actual instruments

## Author Information

**Dr. Mohammed Yagoub Esmail**
- Institution: Sudan University of Science and Technology (SUST)
- Department: Biomedical Engineering (BME)
- Email: <EMAIL>
- Phone: +************, +************

## License

This educational software is developed for academic use. Please contact the author for usage permissions and collaboration opportunities.

## Version History

### v2.0 (2025)
- Enhanced user interface with modern design
- Added multiple measurement modules
- Implemented circuit analysis mode
- Improved data export functionality
- Added comprehensive help system

### v1.0 (Previous)
- Basic signal simulation
- Simple user interface
- Limited module support

## Contributing

This project is primarily for educational use. For suggestions, improvements, or collaboration opportunities, please contact the author.

## Acknowledgments

- Sudan University of Science and Technology
- Biomedical Engineering Department
- Students and educators who provided feedback
- Open-source web technologies community

---

*Developed with ❤️ for biomedical engineering education*
